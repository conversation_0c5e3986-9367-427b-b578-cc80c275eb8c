import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, Card<PERSON>itle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { APP_CONFIG } from '@/constants/app';
import { 
  MapPin, 
  Phone, 
  Mail, 
  Clock, 
  MessageCircle,
  Facebook,
  Twitter,
  Instagram,
  Linkedin
} from 'lucide-react';

const contactMethods = [
  {
    icon: Phone,
    title: 'Phone',
    value: APP_CONFIG.phone,
    description: 'Call us for immediate assistance',
    action: 'Call Now',
  },
  {
    icon: Mail,
    title: 'Email',
    value: APP_CONFIG.email,
    description: 'Send us an email anytime',
    action: 'Send Email',
  },
  {
    icon: MessageCircle,
    title: 'WhatsApp',
    value: '+212 6XX XXX XXX',
    description: 'Chat with us on WhatsApp',
    action: 'Start Chat',
  },
];

const offices = [
  {
    city: 'Casablanca',
    address: '123 Avenue Mohammed V, Anfa',
    phone: '+212 522 XXX XXX',
    hours: 'Mon-Fri: 9:00 AM - 6:00 PM',
  },
  {
    city: '<PERSON><PERSON>',
    address: '456 Avenue Allal <PERSON>, <PERSON><PERSON><PERSON>',
    phone: '+212 537 XXX XXX',
    hours: 'Mon-Fri: 9:00 AM - 6:00 PM',
  },
  {
    city: 'Marrakech',
    address: '789 Avenue Mohammed VI, Gueliz',
    phone: '+212 524 XXX XXX',
    hours: 'Mon-Fri: 9:00 AM - 6:00 PM',
  },
];

const socialLinks = [
  { icon: Facebook, href: '#', label: 'Facebook' },
  { icon: Twitter, href: '#', label: 'Twitter' },
  { icon: Instagram, href: '#', label: 'Instagram' },
  { icon: Linkedin, href: '#', label: 'LinkedIn' },
];

export function ContactInfo() {
  return (
    <div className="space-y-6">
      {/* Contact Methods */}
      <Card>
        <CardHeader>
          <CardTitle>Get in Touch</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {contactMethods.map((method, index) => {
            const Icon = method.icon;
            return (
              <div key={index} className="flex items-start space-x-4">
                <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center flex-shrink-0">
                  <Icon className="h-6 w-6 text-primary" />
                </div>
                <div className="flex-1">
                  <h3 className="font-medium text-gray-900">{method.title}</h3>
                  <p className="text-primary font-medium">{method.value}</p>
                  <p className="text-sm text-gray-600 mb-2">{method.description}</p>
                  <Button size="sm" variant="outline">
                    {method.action}
                  </Button>
                </div>
              </div>
            );
          })}
        </CardContent>
      </Card>

      {/* Office Locations */}
      <Card>
        <CardHeader>
          <CardTitle>Our Offices</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {offices.map((office, index) => (
            <div key={index} className="border-b border-gray-200 last:border-b-0 pb-4 last:pb-0">
              <h3 className="font-medium text-gray-900 mb-2">{office.city}</h3>
              <div className="space-y-2 text-sm text-gray-600">
                <div className="flex items-start">
                  <MapPin className="h-4 w-4 mr-2 mt-0.5 flex-shrink-0" />
                  <span>{office.address}</span>
                </div>
                <div className="flex items-center">
                  <Phone className="h-4 w-4 mr-2 flex-shrink-0" />
                  <span>{office.phone}</span>
                </div>
                <div className="flex items-center">
                  <Clock className="h-4 w-4 mr-2 flex-shrink-0" />
                  <span>{office.hours}</span>
                </div>
              </div>
            </div>
          ))}
        </CardContent>
      </Card>

      {/* Map */}
      <Card>
        <CardHeader>
          <CardTitle>Find Us</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="aspect-video bg-gray-200 rounded-lg flex items-center justify-center text-gray-400 mb-4">
            <div className="text-center">
              <MapPin className="h-12 w-12 mx-auto mb-2" />
              <p>Interactive Map</p>
            </div>
          </div>
          <p className="text-sm text-gray-600">
            Visit our main office in Casablanca or any of our branch locations 
            across Morocco. We&apos;re here to serve you better.
          </p>
        </CardContent>
      </Card>

      {/* Social Media */}
      <Card>
        <CardHeader>
          <CardTitle>Follow Us</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-gray-600 mb-4">
            Stay connected with us on social media for the latest updates, 
            property listings, and market insights.
          </p>
          <div className="flex space-x-3">
            {socialLinks.map((social, index) => {
              const Icon = social.icon;
              return (
                <Button
                  key={index}
                  variant="outline"
                  size="icon"
                  asChild
                >
                  <a href={social.href} aria-label={social.label}>
                    <Icon className="h-4 w-4" />
                  </a>
                </Button>
              );
            })}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
