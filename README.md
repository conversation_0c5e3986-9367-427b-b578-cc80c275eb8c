# Darden Property & Management

A comprehensive real estate website and property management system built for the Moroccan market, featuring modern design, advanced functionality, and seamless user experience.

## 🌟 Features

### Public Website
- **Modern Homepage** with hero section, featured properties, and company stats
- **Property Listings** with advanced search, filters, and map integration
- **Property Details** with image galleries, virtual tours, and inquiry forms
- **Company Pages** (About, Services, Career, Contact)
- **Blog System** with real estate insights and market analysis
- **Responsive Design** optimized for all devices

### Property Management
- **Property CRUD** operations with rich media support
- **Advanced Search & Filtering** by location, type, price, features
- **Interactive Maps** with property markers and nearby amenities
- **Virtual Tours** and 360° property views
- **Inquiry Management** with automated responses
- **Analytics Dashboard** with performance metrics

### User Management
- **Authentication System** with email/password and social login
- **Role-based Access Control** (Admin, Sales Person, User)
- **User Profiles** with preferences and saved properties
- **Dashboard** for property management and analytics

### Admin Panel
- **User Management** with role assignment and permissions
- **Content Management** for blog posts and static pages
- **System Analytics** with detailed reporting
- **Communication Tools** for customer support

## 🚀 Technology Stack

### Frontend
- **Next.js 14** - React framework with App Router
- **TypeScript** - Type-safe development
- **Tailwind CSS** - Utility-first CSS framework
- **Shadcn/ui** - Modern UI component library
- **React Hook Form** - Form handling with validation
- **Zod** - Schema validation

### Backend & Database
- **Supabase** - Backend-as-a-Service with PostgreSQL
- **Row Level Security** - Database-level security policies
- **Real-time Subscriptions** - Live data updates
- **File Storage** - Image and document management

## 📦 Installation

### Prerequisites
- Node.js 18+
- npm or yarn
- Supabase account

### Setup

1. **Clone the repository**
   ```bash
   git clone https://github.com/your-username/darden-pm-v2.git
   cd darden-pm-v2
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Environment Configuration**
   ```bash
   cp .env.example .env.local
   ```

   Update `.env.local` with your Supabase credentials

4. **Start Development Server**
   ```bash
   npm run dev
   ```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.
