import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Building, MessageSquare, Eye, TrendingUp } from 'lucide-react';

const stats = [
  {
    title: 'Total Properties',
    value: '24',
    change: '+2 this month',
    icon: Building,
    color: 'text-blue-600',
  },
  {
    title: 'Active Inquiries',
    value: '12',
    change: '+3 this week',
    icon: MessageSquare,
    color: 'text-green-600',
  },
  {
    title: 'Property Views',
    value: '1,234',
    change: '+15% this month',
    icon: Eye,
    color: 'text-purple-600',
  },
  {
    title: 'Revenue',
    value: '125,000 MAD',
    change: '+8% this month',
    icon: TrendingUp,
    color: 'text-orange-600',
  },
];

export function DashboardStats() {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {stats.map((stat, index) => {
        const Icon = stat.icon;
        return (
          <Card key={index}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">
                {stat.title}
              </CardTitle>
              <Icon className={`h-4 w-4 ${stat.color}`} />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stat.value}</div>
              <p className="text-xs text-gray-600 mt-1">{stat.change}</p>
            </CardContent>
          </Card>
        );
      })}
    </div>
  );
}
