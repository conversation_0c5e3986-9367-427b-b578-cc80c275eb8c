import { NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

// Create admin client to bypass RLS
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function GET() {
  try {
    console.log('API Test: Starting properties fetch with admin client...');

    // Test basic connection with admin client
    const { data, error, count } = await supabaseAdmin
      .from('properties')
      .select('*', { count: 'exact' });

    console.log('API Test: Query result:', { data, error, count });

    if (error) {
      console.error('API Test: Supabase error:', error);
      return NextResponse.json({
        success: false,
        error: error.message,
        details: error
      }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      count: count || 0,
      properties: data || [],
      message: 'Properties fetched successfully'
    });

  } catch (err) {
    console.error('API Test: Unexpected error:', err);
    return NextResponse.json({
      success: false,
      error: err instanceof Error ? err.message : 'Unknown error'
    }, { status: 500 });
  }
}
