-- Enable RLS on all tables
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.properties ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.property_inquiries ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.blog_posts ENABLE ROW LEVEL SECURITY;

-- Users table policies
CREATE POLICY "Users can view their own profile" ON public.users
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update their own profile" ON public.users
    FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Admins can view all users" ON public.users
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.users 
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

CREATE POLICY "Admins can update all users" ON public.users
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM public.users 
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- Properties table policies
CREATE POLICY "Anyone can view published properties" ON public.properties
    FOR SELECT USING (status IN ('available', 'sold', 'rented'));

CREATE POLICY "Property creators can view their properties" ON public.properties
    FOR SELECT USING (created_by = auth.uid());

CREATE POLICY "Admins and sales persons can view all properties" ON public.properties
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.users 
            WHERE id = auth.uid() AND role IN ('admin', 'sales_person')
        )
    );

CREATE POLICY "Admins and sales persons can create properties" ON public.properties
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM public.users 
            WHERE id = auth.uid() AND role IN ('admin', 'sales_person')
        )
    );

CREATE POLICY "Property creators can update their properties" ON public.properties
    FOR UPDATE USING (created_by = auth.uid());

CREATE POLICY "Admins can update all properties" ON public.properties
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM public.users 
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

CREATE POLICY "Admins can delete properties" ON public.properties
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM public.users 
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- Property inquiries table policies
CREATE POLICY "Users can view their own inquiries" ON public.property_inquiries
    FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Admins and sales persons can view all inquiries" ON public.property_inquiries
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.users 
            WHERE id = auth.uid() AND role IN ('admin', 'sales_person')
        )
    );

CREATE POLICY "Anyone can create inquiries" ON public.property_inquiries
    FOR INSERT WITH CHECK (true);

CREATE POLICY "Users can update their own inquiries" ON public.property_inquiries
    FOR UPDATE USING (user_id = auth.uid());

CREATE POLICY "Admins and sales persons can update all inquiries" ON public.property_inquiries
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM public.users 
            WHERE id = auth.uid() AND role IN ('admin', 'sales_person')
        )
    );

-- Blog posts table policies
CREATE POLICY "Anyone can view published blog posts" ON public.blog_posts
    FOR SELECT USING (published = true);

CREATE POLICY "Authors can view their own blog posts" ON public.blog_posts
    FOR SELECT USING (author_id = auth.uid());

CREATE POLICY "Admins can view all blog posts" ON public.blog_posts
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.users 
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

CREATE POLICY "Admins can create blog posts" ON public.blog_posts
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM public.users 
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

CREATE POLICY "Authors can update their own blog posts" ON public.blog_posts
    FOR UPDATE USING (author_id = auth.uid());

CREATE POLICY "Admins can update all blog posts" ON public.blog_posts
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM public.users 
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

CREATE POLICY "Admins can delete blog posts" ON public.blog_posts
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM public.users 
            WHERE id = auth.uid() AND role = 'admin'
        )
    );
