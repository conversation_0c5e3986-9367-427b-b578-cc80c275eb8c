'use client';

import { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import {
  Search,
  X,
  DollarSign,
  Bed,
  Bath,
  Square,
  SlidersHorizontal
} from 'lucide-react';

interface PropertySearchProps {
  onSearch: (filters: SearchFilters) => void;
  onReset: () => void;
  className?: string;
}

export interface SearchFilters {
  searchQuery?: string;
  propertyType?: string;
  status?: string;
  city?: string;
  region?: string;
  minPrice?: number;
  maxPrice?: number;
  minBedrooms?: number;
  maxBedrooms?: number;
  minBathrooms?: number;
  maxBathrooms?: number;
  minArea?: number;
  maxArea?: number;
  features?: string[];
  sortBy?: 'created_at' | 'price' | 'area' | 'title';
  sortOrder?: 'asc' | 'desc';
}

const PROPERTY_TYPES = [
  { value: 'apartment', label: 'Apartment' },
  { value: 'house', label: 'House' },
  { value: 'villa', label: 'Villa' },
  { value: 'commercial', label: 'Commercial' },
  { value: 'land', label: 'Land' },
];

const PROPERTY_FEATURES = [
  'Swimming Pool', 'Garden', 'Garage', 'Security System', 'City View',
  'Air Conditioning', 'Fireplace', 'Balcony', 'Terrace', 'Elevator',
  'Parking', 'Storage', 'Gym', 'Concierge', 'Pet Friendly'
];

const MOROCCAN_CITIES = [
  'Casablanca', 'Rabat', 'Marrakech', 'Fes', 'Tangier', 'Agadir',
  'Meknes', 'Oujda', 'Kenitra', 'Tetouan', 'Safi', 'Mohammedia'
];

export function PropertySearch({ onSearch, onReset, className = '' }: PropertySearchProps) {
  const [filters, setFilters] = useState<SearchFilters>({});
  const [showAdvanced, setShowAdvanced] = useState(false);
  const [selectedFeatures, setSelectedFeatures] = useState<string[]>([]);

  const handleInputChange = (field: keyof SearchFilters, value: string | number | string[] | undefined) => {
    const newFilters = { ...filters, [field]: value };
    setFilters(newFilters);
  };

  const handleFeatureToggle = (feature: string) => {
    const newFeatures = selectedFeatures.includes(feature)
      ? selectedFeatures.filter(f => f !== feature)
      : [...selectedFeatures, feature];

    setSelectedFeatures(newFeatures);
    handleInputChange('features', newFeatures);
  };

  const handleSearch = () => {
    onSearch(filters);
  };

  const handleReset = () => {
    setFilters({});
    setSelectedFeatures([]);
    onReset();
  };

  const hasActiveFilters = Object.keys(filters).some(key => {
    const value = filters[key as keyof SearchFilters];
    return value !== undefined &&
           value !== '' &&
           (Array.isArray(value) ? value.length > 0 : true);
  });

  return (
    <Card className={`${className} bg-white/80 backdrop-blur-xl border-0 shadow-xl`}>
      <CardHeader className="bg-gradient-to-r from-emerald-50/50 to-blue-50/50 border-b border-gray-100/50">
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center">
            <div className="flex items-center justify-center w-10 h-10 bg-gradient-to-br from-emerald-500 to-blue-500 rounded-xl mr-3">
              <Search className="h-5 w-5 text-white" />
            </div>
            <span className="text-xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent">
              Property Search
            </span>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowAdvanced(!showAdvanced)}
            className="flex items-center bg-white/50 backdrop-blur-sm border-gray-300/50 hover:bg-gradient-to-r hover:from-emerald-50 hover:to-blue-50 transition-all duration-300"
          >
            <SlidersHorizontal className="mr-2 h-4 w-4" />
            {showAdvanced ? 'Simple' : 'Advanced'}
          </Button>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6 p-6">
        {/* Modern Basic Search */}
        <div className="flex gap-4">
          <div className="flex-1 relative group">
            <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5 group-focus-within:text-emerald-500 transition-colors" />
            <Input
              placeholder="Search by title, description, or address..."
              value={filters.searchQuery || ''}
              onChange={(e) => handleInputChange('searchQuery', e.target.value)}
              className="pl-12 h-12 bg-white/50 backdrop-blur-sm border-gray-300/50 rounded-xl focus:border-emerald-400 focus:ring-emerald-400/20 transition-all duration-300"
            />
          </div>
          <Button
            onClick={handleSearch}
            className="bg-gradient-to-r from-emerald-500 to-blue-500 hover:from-emerald-600 hover:to-blue-600 text-white border-0 rounded-xl px-6 h-12 font-semibold shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105"
          >
            <Search className="mr-2 h-4 w-4" />
            Search
          </Button>
        </div>

        {/* Modern Quick Filters */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <select
            value={filters.propertyType || ''}
            onChange={(e) => handleInputChange('propertyType', e.target.value)}
            className="bg-white/50 backdrop-blur-sm border border-gray-300/50 rounded-xl px-4 py-3 text-gray-700 font-medium shadow-sm hover:shadow-md transition-all duration-300 focus:ring-2 focus:ring-emerald-400/20 focus:border-emerald-400"
          >
            <option value="">All Types</option>
            {PROPERTY_TYPES.map((type) => (
              <option key={type.value} value={type.value}>
                {type.label}
              </option>
            ))}
          </select>

          <select
            value={filters.city || ''}
            onChange={(e) => handleInputChange('city', e.target.value)}
            className="bg-white/50 backdrop-blur-sm border border-gray-300/50 rounded-xl px-4 py-3 text-gray-700 font-medium shadow-sm hover:shadow-md transition-all duration-300 focus:ring-2 focus:ring-emerald-400/20 focus:border-emerald-400"
          >
            <option value="">All Cities</option>
            {MOROCCAN_CITIES.map((city) => (
              <option key={city} value={city}>
                {city}
              </option>
            ))}
          </select>

          <select
            value={filters.sortBy || 'created_at'}
            onChange={(e) => handleInputChange('sortBy', e.target.value)}
            className="bg-white/50 backdrop-blur-sm border border-gray-300/50 rounded-xl px-4 py-3 text-gray-700 font-medium shadow-sm hover:shadow-md transition-all duration-300 focus:ring-2 focus:ring-emerald-400/20 focus:border-emerald-400"
          >
            <option value="created_at">Latest</option>
            <option value="price">Price</option>
            <option value="area">Area</option>
            <option value="title">Name</option>
          </select>

          <select
            value={filters.sortOrder || 'desc'}
            onChange={(e) => handleInputChange('sortOrder', e.target.value)}
            className="bg-white/50 backdrop-blur-sm border border-gray-300/50 rounded-xl px-4 py-3 text-gray-700 font-medium shadow-sm hover:shadow-md transition-all duration-300 focus:ring-2 focus:ring-emerald-400/20 focus:border-emerald-400"
          >
            <option value="desc">High to Low</option>
            <option value="asc">Low to High</option>
          </select>
        </div>

        {/* Advanced Filters */}
        {showAdvanced && (
          <div className="space-y-6 border-t pt-6 scale-in">
            {/* Price Range */}
            <div>
              <h4 className="text-sm font-medium text-gray-900 mb-3 flex items-center">
                <DollarSign className="mr-2 h-4 w-4" />
                Price Range (MAD)
              </h4>
              <div className="grid grid-cols-2 gap-4">
                <Input
                  type="number"
                  placeholder="Min price"
                  value={filters.minPrice || ''}
                  onChange={(e) => handleInputChange('minPrice', e.target.value ? Number(e.target.value) : undefined)}
                />
                <Input
                  type="number"
                  placeholder="Max price"
                  value={filters.maxPrice || ''}
                  onChange={(e) => handleInputChange('maxPrice', e.target.value ? Number(e.target.value) : undefined)}
                />
              </div>
            </div>

            {/* Bedrooms & Bathrooms */}
            <div className="grid grid-cols-2 gap-6">
              <div>
                <h4 className="text-sm font-medium text-gray-900 mb-3 flex items-center">
                  <Bed className="mr-2 h-4 w-4" />
                  Bedrooms
                </h4>
                <div className="grid grid-cols-2 gap-2">
                  <Input
                    type="number"
                    placeholder="Min"
                    value={filters.minBedrooms || ''}
                    onChange={(e) => handleInputChange('minBedrooms', e.target.value ? Number(e.target.value) : undefined)}
                  />
                  <Input
                    type="number"
                    placeholder="Max"
                    value={filters.maxBedrooms || ''}
                    onChange={(e) => handleInputChange('maxBedrooms', e.target.value ? Number(e.target.value) : undefined)}
                  />
                </div>
              </div>

              <div>
                <h4 className="text-sm font-medium text-gray-900 mb-3 flex items-center">
                  <Bath className="mr-2 h-4 w-4" />
                  Bathrooms
                </h4>
                <div className="grid grid-cols-2 gap-2">
                  <Input
                    type="number"
                    placeholder="Min"
                    value={filters.minBathrooms || ''}
                    onChange={(e) => handleInputChange('minBathrooms', e.target.value ? Number(e.target.value) : undefined)}
                  />
                  <Input
                    type="number"
                    placeholder="Max"
                    value={filters.maxBathrooms || ''}
                    onChange={(e) => handleInputChange('maxBathrooms', e.target.value ? Number(e.target.value) : undefined)}
                  />
                </div>
              </div>
            </div>

            {/* Area */}
            <div>
              <h4 className="text-sm font-medium text-gray-900 mb-3 flex items-center">
                <Square className="mr-2 h-4 w-4" />
                Area (m²)
              </h4>
              <div className="grid grid-cols-2 gap-4">
                <Input
                  type="number"
                  placeholder="Min area"
                  value={filters.minArea || ''}
                  onChange={(e) => handleInputChange('minArea', e.target.value ? Number(e.target.value) : undefined)}
                />
                <Input
                  type="number"
                  placeholder="Max area"
                  value={filters.maxArea || ''}
                  onChange={(e) => handleInputChange('maxArea', e.target.value ? Number(e.target.value) : undefined)}
                />
              </div>
            </div>

            {/* Features */}
            <div>
              <h4 className="text-sm font-medium text-gray-900 mb-3">Features & Amenities</h4>
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2">
                {PROPERTY_FEATURES.map((feature) => (
                  <button
                    key={feature}
                    type="button"
                    onClick={() => handleFeatureToggle(feature)}
                    className={`p-2 text-xs border rounded-md transition-colors ${
                      selectedFeatures.includes(feature)
                        ? 'bg-italian-green text-white border-italian-green'
                        : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
                    }`}
                  >
                    {feature}
                  </button>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* Active Filters */}
        {hasActiveFilters && (
          <div className="border-t pt-4">
            <div className="flex items-center justify-between mb-2">
              <h4 className="text-sm font-medium text-gray-900">Active Filters</h4>
              <Button variant="ghost" size="sm" onClick={handleReset}>
                <X className="mr-1 h-3 w-3" />
                Clear All
              </Button>
            </div>
            <div className="flex flex-wrap gap-2">
              {filters.searchQuery && (
                <Badge variant="secondary" className="flex items-center">
                  Search: {filters.searchQuery}
                  <X
                    className="ml-1 h-3 w-3 cursor-pointer"
                    onClick={() => handleInputChange('searchQuery', '')}
                  />
                </Badge>
              )}
              {filters.propertyType && (
                <Badge variant="secondary" className="flex items-center">
                  Type: {PROPERTY_TYPES.find(t => t.value === filters.propertyType)?.label}
                  <X
                    className="ml-1 h-3 w-3 cursor-pointer"
                    onClick={() => handleInputChange('propertyType', '')}
                  />
                </Badge>
              )}
              {filters.city && (
                <Badge variant="secondary" className="flex items-center">
                  City: {filters.city}
                  <X
                    className="ml-1 h-3 w-3 cursor-pointer"
                    onClick={() => handleInputChange('city', '')}
                  />
                </Badge>
              )}
              {selectedFeatures.map((feature) => (
                <Badge key={feature} variant="secondary" className="flex items-center">
                  {feature}
                  <X
                    className="ml-1 h-3 w-3 cursor-pointer"
                    onClick={() => handleFeatureToggle(feature)}
                  />
                </Badge>
              ))}
            </div>
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex justify-between pt-4 border-t">
          <Button variant="outline" onClick={handleReset} disabled={!hasActiveFilters}>
            Reset Filters
          </Button>
          <Button onClick={handleSearch} className="bg-primary hover:bg-primary/90">
            Apply Filters
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
