import { Card, CardContent } from '@/components/ui/card';
import { Star } from 'lucide-react';

const testimonials = [
  {
    name: '<PERSON>',
    role: 'Property Investor',
    content: 'Darden <PERSON> helped me find the perfect investment property in Casablanca. Their expertise and professionalism made the entire process smooth and stress-free.',
    rating: 5,
    location: 'Casablanca',
  },
  {
    name: '<PERSON>',
    role: 'Expatriate',
    content: 'As a foreigner looking for property in Morocco, I was impressed by their knowledge of the local market and their ability to guide me through the legal processes.',
    rating: 5,
    location: 'Rabat',
  },
  {
    name: '<PERSON>',
    role: 'First-time Buyer',
    content: 'The team at Darden PM made my dream of owning a traditional riad in Marrakech come true. Their attention to detail and customer service is exceptional.',
    rating: 5,
    location: 'Marrakech',
  },
];

export function TestimonialsSection() {
  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
            What Our Clients Say
          </h2>
          <p className="text-gray-600 max-w-2xl mx-auto">
            Don&apos;t just take our word for it. Here&apos;s what our satisfied clients
            have to say about their experience with Darden Property & Management.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {testimonials.map((testimonial, index) => (
            <Card key={index} className="h-full">
              <CardContent className="p-6">
                <div className="flex items-center mb-4">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <Star key={i} className="h-5 w-5 text-yellow-400 fill-current" />
                  ))}
                </div>
                
                <blockquote className="text-gray-700 mb-6 italic">
                  &ldquo;{testimonial.content}&rdquo;
                </blockquote>
                
                <div className="border-t pt-4">
                  <div className="font-semibold text-gray-900">
                    {testimonial.name}
                  </div>
                  <div className="text-sm text-gray-600">
                    {testimonial.role} • {testimonial.location}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
}
