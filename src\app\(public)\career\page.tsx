import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  MapPin, 
  Clock, 
  DollarSign, 
  Users, 
  TrendingUp, 
  Heart,
  Briefcase,
  GraduationCap,
  Coffee,
  Shield
} from 'lucide-react';
import Link from 'next/link';

const openPositions = [
  {
    title: 'Senior Real Estate Agent',
    department: 'Sales',
    location: 'Casablanca',
    type: 'Full-time',
    salary: '15,000 - 25,000 MAD',
    description: 'Join our sales team to help clients find their dream properties in Casablanca.',
    requirements: ['3+ years real estate experience', 'Fluent in Arabic, French, English', 'Valid driving license'],
  },
  {
    title: 'Property Manager',
    department: 'Operations',
    location: 'Rabat',
    type: 'Full-time',
    salary: '12,000 - 18,000 MAD',
    description: 'Manage our growing portfolio of rental properties and ensure tenant satisfaction.',
    requirements: ['Property management experience', 'Strong communication skills', 'Problem-solving abilities'],
  },
  {
    title: 'Digital Marketing Specialist',
    department: 'Marketing',
    location: 'Remote',
    type: 'Full-time',
    salary: '10,000 - 15,000 MAD',
    description: 'Drive our digital marketing efforts and enhance our online presence.',
    requirements: ['Digital marketing experience', 'Social media expertise', 'Content creation skills'],
  },
  {
    title: 'Junior Sales Associate',
    department: 'Sales',
    location: 'Marrakech',
    type: 'Full-time',
    salary: '8,000 - 12,000 MAD',
    description: 'Start your real estate career with comprehensive training and mentorship.',
    requirements: ['Bachelor\'s degree', 'Excellent communication', 'Eager to learn'],
  },
];

const benefits = [
  {
    icon: DollarSign,
    title: 'Competitive Salary',
    description: 'Market-leading compensation packages with performance bonuses.',
  },
  {
    icon: TrendingUp,
    title: 'Career Growth',
    description: 'Clear advancement paths and professional development opportunities.',
  },
  {
    icon: GraduationCap,
    title: 'Training & Development',
    description: 'Continuous learning programs and industry certifications.',
  },
  {
    icon: Shield,
    title: 'Health Insurance',
    description: 'Comprehensive health coverage for you and your family.',
  },
  {
    icon: Coffee,
    title: 'Flexible Work',
    description: 'Hybrid work options and flexible scheduling.',
  },
  {
    icon: Heart,
    title: 'Team Culture',
    description: 'Collaborative environment with team building activities.',
  },
];

const values = [
  'Excellence in everything we do',
  'Integrity and transparency',
  'Client-first mentality',
  'Continuous innovation',
  'Teamwork and collaboration',
  'Community involvement',
];

export default function CareerPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-100">
      <div className="container mx-auto px-4 py-16">
        {/* Hero Section */}
        <div className="text-center mb-20 relative">
          <div className="absolute inset-0 bg-gradient-to-r from-italian-green/5 via-white/50 to-italian-red/5 rounded-3xl -z-10"></div>
          <div className="relative py-16">
            <Badge variant="secondary" className="mb-6 bg-gradient-to-r from-italian-green to-italian-red text-white border-0 px-6 py-2 text-sm font-medium animate-fade-in">
              Careers
            </Badge>
            <h1 className="text-5xl lg:text-6xl font-bold text-gray-900 mb-8 italian-flag-corner animate-slide-up">
              Join Our Growing Team
            </h1>
            <p className="text-xl lg:text-2xl text-gray-600 max-w-4xl mx-auto leading-relaxed animate-fade-in-delay">
              Build your career with Morocco&apos;s leading real estate company. We&apos;re looking for
              passionate professionals to help shape the future of Moroccan real estate.
            </p>
          </div>
        </div>

        {/* Why Join Us */}
        <div className="mb-24 relative">
          <div className="absolute inset-0 bg-gradient-to-r from-italian-green/3 via-transparent to-italian-red/3 rounded-3xl"></div>
          <div className="relative py-16">
            <div className="text-center mb-16">
              <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6 italian-flag-corner animate-slide-up">
                Why Work With Us?
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed animate-fade-in-delay">
                We believe our people are our greatest asset. That&apos;s why we invest in creating
                an environment where talent thrives and careers flourish.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {benefits.map((benefit, index) => {
                const Icon = benefit.icon;
                return (
                  <Card key={index} className="text-center hover:shadow-xl transition-all duration-500 hover-lift group bg-gradient-to-br from-white to-gray-50 border-0 shadow-lg animate-scale-in" style={{ animationDelay: `${index * 100}ms` }}>
                    <CardContent className="pt-10 pb-8">
                      <div className="w-20 h-20 bg-gradient-to-br from-italian-green to-italian-red rounded-full flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300 shadow-lg">
                        <Icon className="h-10 w-10 text-white" />
                      </div>
                      <h3 className="text-xl font-bold text-gray-900 mb-4 group-hover:text-italian-green transition-colors duration-300">
                        {benefit.title}
                      </h3>
                      <p className="text-gray-600 leading-relaxed">
                        {benefit.description}
                      </p>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          </div>
        </div>

        {/* Open Positions */}
        <div className="mb-24">
          <div className="text-center mb-16">
            <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6 italian-flag-corner animate-slide-up">
              Open Positions
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed animate-fade-in-delay">
              Explore our current job openings and find the perfect role to advance your career.
            </p>
          </div>

          <div className="space-y-8">
            {openPositions.map((position, index) => (
              <Card key={index} className="hover:shadow-xl transition-all duration-500 hover-lift group bg-gradient-to-br from-white to-gray-50 border-0 shadow-lg animate-scale-in" style={{ animationDelay: `${index * 150}ms` }}>
                <CardHeader className="pb-4">
                  <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                    <div className="flex-1">
                      <CardTitle className="text-2xl mb-4 group-hover:text-italian-green transition-colors duration-300">{position.title}</CardTitle>
                      <div className="flex flex-wrap gap-3 mb-2">
                        <Badge variant="secondary" className="bg-gradient-to-r from-italian-green to-italian-red text-white border-0 px-3 py-1">
                          {position.department}
                        </Badge>
                        <div className="flex items-center text-sm text-gray-600 bg-gray-100 px-3 py-1 rounded-full">
                          <MapPin className="h-4 w-4 mr-1 text-italian-green" />
                          {position.location}
                        </div>
                        <div className="flex items-center text-sm text-gray-600 bg-gray-100 px-3 py-1 rounded-full">
                          <Clock className="h-4 w-4 mr-1 text-italian-green" />
                          {position.type}
                        </div>
                        <div className="flex items-center text-sm text-gray-600 bg-gray-100 px-3 py-1 rounded-full">
                          <DollarSign className="h-4 w-4 mr-1 text-italian-green" />
                          {position.salary}
                        </div>
                      </div>
                    </div>
                    <Button className="mt-6 lg:mt-0 bg-gradient-to-r from-italian-green to-italian-red hover:from-italian-red hover:to-italian-green transition-all duration-300 border-0 px-8 py-3 text-lg font-semibold shadow-lg hover:shadow-xl">
                      Apply Now
                    </Button>
                  </div>
                </CardHeader>
                <CardContent className="pt-0">
                  <p className="text-gray-600 mb-6 text-lg leading-relaxed">{position.description}</p>
                  <div className="bg-gradient-to-r from-gray-50 to-white p-6 rounded-xl">
                    <h4 className="font-bold text-gray-900 mb-4 text-lg">Requirements:</h4>
                    <ul className="space-y-2">
                      {position.requirements.map((req, idx) => (
                        <li key={idx} className="flex items-start text-gray-700">
                          <div className="w-2 h-2 bg-gradient-to-r from-italian-green to-italian-red rounded-full mr-3 mt-2 flex-shrink-0"></div>
                          <span>{req}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Company Culture */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center mb-24 relative">
          <div className="absolute inset-0 bg-gradient-to-br from-italian-green/5 via-transparent to-italian-red/5 rounded-3xl -z-10"></div>
          <div className="relative py-12">
            <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-8 italian-flag-corner animate-slide-up">
              Our Culture & Values
            </h2>
            <p className="text-xl text-gray-600 mb-8 leading-relaxed animate-fade-in-delay">
              At Darden Property & Management, we foster a culture of excellence,
              innovation, and mutual respect. Our values guide our decisions and
              shape our interactions with clients and colleagues.
            </p>
            <ul className="space-y-4">
              {values.map((value, index) => (
                <li key={index} className="flex items-center text-gray-700 text-lg animate-scale-in" style={{ animationDelay: `${index * 100}ms` }}>
                  <div className="w-3 h-3 bg-gradient-to-r from-italian-green to-italian-red rounded-full mr-4 flex-shrink-0"></div>
                  <span className="font-medium">{value}</span>
                </li>
              ))}
            </ul>
          </div>
          <div className="aspect-[4/3] bg-gradient-to-br from-italian-green/10 to-italian-red/10 rounded-2xl flex items-center justify-center shadow-xl hover-lift transition-all duration-500 animate-scale-in">
            <div className="text-center">
              <div className="w-24 h-24 bg-gradient-to-br from-italian-green to-italian-red rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg">
                <Users className="h-12 w-12 text-white" />
              </div>
              <p className="text-gray-600 text-lg font-medium">Team Culture & Values</p>
              <p className="text-gray-500 text-sm mt-2">Building Excellence Together</p>
            </div>
          </div>
        </div>

        {/* Application Process */}
        <div className="bg-gradient-to-br from-gray-50 via-white to-gray-100 rounded-3xl p-12 lg:p-16 mb-24 shadow-xl relative overflow-hidden">
          <div className="absolute top-0 left-0 w-full h-2 bg-gradient-to-r from-italian-green via-white to-italian-red"></div>
          <div className="text-center mb-16">
            <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6 italian-flag-corner animate-slide-up">
              Application Process
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed animate-fade-in-delay">
              Our streamlined hiring process is designed to find the best talent
              while providing a positive candidate experience.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            {[
              { step: '01', title: 'Apply Online', desc: 'Submit your application and resume' },
              { step: '02', title: 'Initial Review', desc: 'We review your qualifications' },
              { step: '03', title: 'Interview', desc: 'Meet with our hiring team' },
              { step: '04', title: 'Welcome Aboard', desc: 'Join our team and start growing' },
            ].map((step, index) => (
              <div key={index} className="text-center group animate-scale-in" style={{ animationDelay: `${index * 150}ms` }}>
                <div className="w-20 h-20 bg-gradient-to-br from-italian-green to-italian-red rounded-full flex items-center justify-center text-white font-bold text-2xl mb-6 mx-auto shadow-lg group-hover:scale-110 transition-transform duration-300">
                  {step.step}
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-3 group-hover:text-italian-green transition-colors duration-300">
                  {step.title}
                </h3>
                <p className="text-gray-600 leading-relaxed">
                  {step.desc}
                </p>
                {index < 3 && (
                  <div className="hidden md:block absolute top-10 left-full w-8 h-0.5 bg-gradient-to-r from-italian-green to-italian-red transform -translate-x-4"></div>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* CTA */}
        <div className="text-center bg-gradient-to-br from-italian-green via-primary to-italian-red rounded-3xl p-12 lg:p-16 text-white relative overflow-hidden shadow-2xl">
          <div className="absolute inset-0 bg-black/10 backdrop-blur-sm"></div>
          <div className="relative z-10">
            <h2 className="text-4xl lg:text-5xl font-bold mb-6 animate-slide-up">
              Don&apos;t See the Right Role?
            </h2>
            <p className="text-xl lg:text-2xl mb-10 opacity-95 max-w-3xl mx-auto leading-relaxed animate-fade-in-delay">
              We&apos;re always looking for talented individuals. Send us your resume
              and we&apos;ll keep you in mind for future opportunities.
            </p>
            <div className="flex flex-col sm:flex-row gap-6 justify-center animate-scale-in">
              <Button size="lg" variant="secondary" className="bg-white text-italian-green hover:bg-gray-100 border-0 px-8 py-4 text-lg font-semibold shadow-lg hover:shadow-xl transition-all duration-300 hover-lift">
                <Briefcase className="mr-3 h-6 w-6" />
                Send Your Resume
              </Button>
              <Button size="lg" variant="outline" className="bg-transparent border-2 border-white text-white hover:bg-white hover:text-italian-green px-8 py-4 text-lg font-semibold shadow-lg hover:shadow-xl transition-all duration-300 hover-lift" asChild>
                <Link href="/contact">
                  <Heart className="mr-3 h-6 w-6" />
                  Contact HR
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
