'use client';

import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Bed, Bath, Square, MapPin, Eye } from 'lucide-react';
import Link from 'next/link';

interface SimilarPropertiesProps {
  propertyId: string;
}

export function SimilarProperties({
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  propertyId
}: SimilarPropertiesProps) {
  // Mock data - will be replaced with real data from Supabase
  const similarProperties = [
    {
      id: '2',
      title: 'Modern Apartment in Rabat',
      price: 850000,
      currency: 'MAD',
      location: 'Agdal, Rabat',
      bedrooms: 3,
      bathrooms: 2,
      area: 120,
      status: 'available',
      type: 'apartment',
    },
    {
      id: '3',
      title: 'Traditional Riad in Marrakech',
      price: 1200000,
      currency: 'MAD',
      location: 'Medina, Marrakech',
      bedrooms: 4,
      bathrooms: 3,
      area: 200,
      status: 'available',
      type: 'house',
    },
    {
      id: '4',
      title: 'Beachfront Villa in Agadir',
      price: 1800000,
      currency: 'MAD',
      location: 'Marina District, Agadir',
      bedrooms: 4,
      bathrooms: 3,
      area: 300,
      status: 'available',
      type: 'villa',
    },
  ];

  return (
    <section>
      <h2 className="text-2xl font-bold text-gray-900 mb-6">
        Similar Properties
      </h2>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {similarProperties.map((property) => (
          <Card key={property.id} className="overflow-hidden hover:shadow-lg transition-shadow">
            <div className="relative">
              <div className="aspect-[4/3] bg-gray-200 relative">
                <div className="absolute inset-0 flex items-center justify-center text-gray-400">
                  <Eye className="h-12 w-12" />
                </div>
              </div>
              <div className="absolute top-4 left-4">
                <Badge variant="success">
                  {property.status === 'available' ? 'Available' : property.status}
                </Badge>
              </div>
              <div className="absolute top-4 right-4">
                <Badge variant="secondary">
                  {property.type}
                </Badge>
              </div>
            </div>
            
            <CardContent className="p-6">
              <div className="mb-4">
                <h3 className="text-lg font-semibold text-gray-900 mb-2 line-clamp-2">
                  {property.title}
                </h3>
                <div className="flex items-center text-gray-600 mb-2">
                  <MapPin className="h-4 w-4 mr-1 flex-shrink-0" />
                  <span className="text-sm line-clamp-1">{property.location}</span>
                </div>
                <div className="text-xl font-bold text-primary">
                  {property.price.toLocaleString()} {property.currency}
                </div>
              </div>

              <div className="flex items-center justify-between text-sm text-gray-600 mb-4">
                <div className="flex items-center">
                  <Bed className="h-4 w-4 mr-1" />
                  <span>{property.bedrooms}</span>
                </div>
                <div className="flex items-center">
                  <Bath className="h-4 w-4 mr-1" />
                  <span>{property.bathrooms}</span>
                </div>
                <div className="flex items-center">
                  <Square className="h-4 w-4 mr-1" />
                  <span>{property.area} m²</span>
                </div>
              </div>

              <Button asChild className="w-full">
                <Link href={`/properties/${property.id}`}>
                  View Details
                </Link>
              </Button>
            </CardContent>
          </Card>
        ))}
      </div>
    </section>
  );
}
