{"buildCommand": "npm run build", "outputDirectory": ".next", "framework": "nextjs", "installCommand": "npm install", "devCommand": "npm run dev", "env": {"NEXT_PUBLIC_SUPABASE_URL": "@supabase-url", "NEXT_PUBLIC_SUPABASE_ANON_KEY": "@supabase-anon-key", "SUPABASE_SERVICE_ROLE_KEY": "@supabase-service-role-key", "NEXT_PUBLIC_APP_URL": "@app-url"}, "functions": {"app/api/**/*.ts": {"maxDuration": 30}}, "headers": [{"source": "/(.*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}]}], "redirects": [{"source": "/admin", "destination": "/admin/dashboard", "permanent": false}], "rewrites": [{"source": "/sitemap.xml", "destination": "/api/sitemap"}, {"source": "/robots.txt", "destination": "/api/robots"}]}