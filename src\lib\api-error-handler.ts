import { NextResponse } from 'next/server';
import { config } from './config';

export interface ApiError {
  message: string;
  code?: string;
  statusCode: number;
  details?: unknown;
  stack?: string;
}

export class AppError extends Error {
  public readonly statusCode: number;
  public readonly code?: string;
  public readonly details?: unknown;
  public readonly isOperational: boolean;

  constructor(
    message: string,
    statusCode: number = 500,
    code?: string,
    details?: unknown,
    isOperational: boolean = true
  ) {
    super(message);
    this.statusCode = statusCode;
    this.code = code;
    this.details = details;
    this.isOperational = isOperational;

    Error.captureStackTrace(this, this.constructor);
  }
}

// Common error types
export class ValidationError extends AppError {
  constructor(message: string, details?: unknown) {
    super(message, 400, 'VALIDATION_ERROR', details);
  }
}

export class AuthenticationError extends AppError {
  constructor(message: string = 'Authentication required') {
    super(message, 401, 'AUTHENTICATION_ERROR');
  }
}

export class AuthorizationError extends AppError {
  constructor(message: string = 'Insufficient permissions') {
    super(message, 403, 'AUTHORIZATION_ERROR');
  }
}

export class NotFoundError extends AppError {
  constructor(message: string = 'Resource not found') {
    super(message, 404, 'NOT_FOUND_ERROR');
  }
}

export class ConflictError extends AppError {
  constructor(message: string, details?: unknown) {
    super(message, 409, 'CONFLICT_ERROR', details);
  }
}

export class RateLimitError extends AppError {
  constructor(message: string = 'Rate limit exceeded') {
    super(message, 429, 'RATE_LIMIT_ERROR');
  }
}

export class DatabaseError extends AppError {
  constructor(message: string, details?: unknown) {
    super(message, 500, 'DATABASE_ERROR', details, false);
  }
}

export class ExternalServiceError extends AppError {
  constructor(message: string, details?: unknown) {
    super(message, 502, 'EXTERNAL_SERVICE_ERROR', details, false);
  }
}

// Error handler for API routes
export function handleApiError(error: unknown): NextResponse {
  console.error('API Error:', error);

  // Log error to external service in production
  if (config.isProduction) {
    logErrorToService(error);
  }

  if (error instanceof AppError) {
    return NextResponse.json(
      {
        error: {
          message: error.message,
          code: error.code,
          ...(config.isDevelopment && { details: error.details }),
        },
      },
      { status: error.statusCode }
    );
  }

  // Handle Supabase errors
  if (error && typeof error === 'object' && 'code' in error) {
    const supabaseError = error as { code: string; message?: string; details?: unknown };
    
    switch (supabaseError.code) {
      case '23505': // Unique violation
        return NextResponse.json(
          {
            error: {
              message: 'Resource already exists',
              code: 'DUPLICATE_RESOURCE',
            },
          },
          { status: 409 }
        );
      
      case '23503': // Foreign key violation
        return NextResponse.json(
          {
            error: {
              message: 'Referenced resource not found',
              code: 'INVALID_REFERENCE',
            },
          },
          { status: 400 }
        );
      
      case 'PGRST116': // No rows found
        return NextResponse.json(
          {
            error: {
              message: 'Resource not found',
              code: 'NOT_FOUND',
            },
          },
          { status: 404 }
        );
      
      default:
        return NextResponse.json(
          {
            error: {
              message: 'Database operation failed',
              code: 'DATABASE_ERROR',
              ...(config.isDevelopment && { details: supabaseError }),
            },
          },
          { status: 500 }
        );
    }
  }

  // Handle validation errors from zod or similar
  if (error && typeof error === 'object' && 'issues' in error) {
    const validationError = error as { issues: unknown[] };
    return NextResponse.json(
      {
        error: {
          message: 'Validation failed',
          code: 'VALIDATION_ERROR',
          details: validationError.issues,
        },
      },
      { status: 400 }
    );
  }

  // Generic error fallback
  const message = config.isDevelopment 
    ? (error instanceof Error ? error.message : 'Unknown error occurred')
    : 'Internal server error';

  return NextResponse.json(
    {
      error: {
        message,
        code: 'INTERNAL_ERROR',
        ...(config.isDevelopment && error instanceof Error && { stack: error.stack }),
      },
    },
    { status: 500 }
  );
}

// Async wrapper for API routes
export function withErrorHandler(
  handler: (request: Request, context?: unknown) => Promise<NextResponse>
) {
  return async (request: Request, context?: unknown): Promise<NextResponse> => {
    try {
      return await handler(request, context);
    } catch (error) {
      return handleApiError(error);
    }
  };
}

// Log error to external service
function logErrorToService(error: unknown) {
  try {
    // Example: Send to Sentry, LogRocket, or custom logging service
    const errorData = {
      message: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
      timestamp: new Date().toISOString(),
      environment: config.app.environment,
      version: process.env.npm_package_version,
    };

    // In a real application, you would send this to your logging service
    console.error('Error logged to service:', errorData);
    
    // Example Sentry integration:
    // Sentry.captureException(error);
    
    // Example custom logging service:
    // fetch('https://your-logging-service.com/api/errors', {
    //   method: 'POST',
    //   headers: { 'Content-Type': 'application/json' },
    //   body: JSON.stringify(errorData),
    // });
  } catch (loggingError) {
    console.error('Failed to log error to service:', loggingError);
  }
}

// Utility to create standardized API responses
export function createApiResponse<T>(
  data: T,
  message?: string,
  statusCode: number = 200
): NextResponse {
  return NextResponse.json(
    {
      success: true,
      data,
      message,
      timestamp: new Date().toISOString(),
    },
    { status: statusCode }
  );
}

// Utility to create error responses
export function createErrorResponse(
  message: string,
  code?: string,
  statusCode: number = 400,
  details?: unknown
): NextResponse {
  const errorObject: { message: string; code?: string; details?: unknown } = {
    message,
    code,
  };

  if (config.isDevelopment && details) {
    errorObject.details = details;
  }

  return NextResponse.json(
    {
      success: false,
      error: errorObject,
      timestamp: new Date().toISOString(),
    },
    { status: statusCode }
  );
}

export default handleApiError;
