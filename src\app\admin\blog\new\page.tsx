import { BlogEditor } from '@/components/features/admin/blog-editor';

export default function NewBlogPostPage() {
  return (
    <div className="space-y-10">
      <div className="relative">
        <div className="absolute inset-0 bg-gradient-to-r from-italian-green/5 via-white/50 to-italian-red/5 rounded-2xl -z-10"></div>
        <div className="relative py-8">
          <h1 className="text-4xl lg:text-5xl font-bold text-gray-900 italian-flag-corner animate-slide-up mb-3">
            Create New Blog Post
          </h1>
          <p className="text-xl text-gray-600 animate-fade-in-delay">
            Write and publish a new blog post or article.
          </p>
        </div>
      </div>

      <BlogEditor />
    </div>
  );
}
