import { z } from 'zod';

export const propertySchema = z.object({
  title: z.string().min(5, 'Title must be at least 5 characters'),
  description: z.string().min(20, 'Description must be at least 20 characters'),
  price: z.number().positive('Price must be a positive number'),
  currency: z.string().min(1, 'Currency is required'),
  property_type: z.enum(['apartment', 'house', 'villa', 'commercial', 'land']),
  status: z.enum(['available', 'sold', 'rented', 'pending']),
  bedrooms: z.number().int().min(0).optional(),
  bathrooms: z.number().int().min(0).optional(),
  area: z.number().positive().optional(),
  address: z.string().min(5, 'Address must be at least 5 characters'),
  city: z.string().min(2, 'City must be at least 2 characters'),
  region: z.string().min(2, 'Region must be at least 2 characters'),
  country: z.string().min(1, 'Country is required'),
  features: z.array(z.string()).optional(),
  virtual_tour_url: z.string().optional(),
});

export const propertyInquirySchema = z.object({
  property_id: z.string().uuid(),
  name: z.string().min(2, 'Name must be at least 2 characters'),
  email: z.string().email('Please enter a valid email address'),
  phone: z.string().optional(),
  message: z.string().min(10, 'Message must be at least 10 characters'),
  inquiry_type: z.enum(['purchase', 'rent', 'information']),
});

export const propertySearchSchema = z.object({
  query: z.string().optional(),
  property_type: z.enum(['apartment', 'house', 'villa', 'commercial', 'land']).optional(),
  min_price: z.number().positive().optional(),
  max_price: z.number().positive().optional(),
  bedrooms: z.number().int().min(0).optional(),
  bathrooms: z.number().int().min(0).optional(),
  city: z.string().optional(),
  region: z.string().optional(),
  features: z.array(z.string()).optional(),
});

export type PropertyInput = z.infer<typeof propertySchema>;
export type PropertyInquiryInput = z.infer<typeof propertyInquirySchema>;
export type PropertySearchInput = z.infer<typeof propertySearchSchema>;
