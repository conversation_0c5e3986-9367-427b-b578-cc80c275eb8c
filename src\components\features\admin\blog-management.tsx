'use client';

import { useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { 
  Search, 
  Filter, 
  Plus, 
  Edit, 
  Trash2, 
  Eye, 
  Calendar,
  User,
  MoreHorizontal,
  FileText
} from 'lucide-react';

// Mock blog posts data
const blogPosts = [
  {
    id: '1',
    title: 'Morocco Real Estate Market Trends 2024',
    slug: 'morocco-real-estate-trends-2024',
    excerpt: 'Discover the latest trends shaping Morocco\'s real estate market in 2024...',
    category: 'Market Analysis',
    author: '<PERSON>',
    status: 'published',
    publishedAt: '2024-01-15',
    views: 1234,
    comments: 23
  },
  {
    id: '2',
    title: 'FIFA World Cup 2030: Impact on Moroccan Property Market',
    slug: 'fifa-2030-morocco-property-impact',
    excerpt: 'How the FIFA World Cup 2030 is transforming Morocco\'s property landscape...',
    category: 'Investment',
    author: '<PERSON><PERSON>',
    status: 'published',
    publishedAt: '2024-01-10',
    views: 987,
    comments: 18
  },
  {
    id: '3',
    title: 'Best Neighborhoods to Invest in Casablanca',
    slug: 'best-neighborhoods-casablanca-investment',
    excerpt: 'A comprehensive guide to the most promising neighborhoods in Casablanca...',
    category: 'Investment Guide',
    author: 'Sarah El Fassi',
    status: 'draft',
    publishedAt: null,
    views: 0,
    comments: 0
  },
  {
    id: '4',
    title: 'Understanding Moroccan Property Laws for Foreigners',
    slug: 'moroccan-property-laws-foreigners',
    excerpt: 'Everything foreign investors need to know about buying property in Morocco...',
    category: 'Legal Guide',
    author: 'Ahmed Benali',
    status: 'published',
    publishedAt: '2024-01-03',
    views: 2156,
    comments: 45
  }
];

export function BlogManagement() {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedStatus, setSelectedStatus] = useState('all');

  const filteredPosts = blogPosts.filter(post => {
    const matchesSearch = post.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         post.category.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesStatus = selectedStatus === 'all' || post.status === selectedStatus;
    return matchesSearch && matchesStatus;
  });

  const getStatusColor = (status: string): 'success' | 'warning' | 'secondary' => {
    switch (status) {
      case 'published': return 'success';
      case 'draft': return 'warning';
      default: return 'secondary';
    }
  };

  return (
    <div className="space-y-8">
      {/* Header Actions */}
      <div className="bg-gradient-to-r from-white to-gray-50 rounded-2xl p-6 shadow-lg border-0">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
          <div className="flex flex-col sm:flex-row items-start sm:items-center space-y-4 sm:space-y-0 sm:space-x-6">
            <div className="relative">
              <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-italian-green h-5 w-5" />
              <Input
                placeholder="Search blog posts..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-12 w-80 h-12 border-2 border-gray-200 focus:border-italian-green rounded-xl bg-white shadow-sm"
              />
            </div>
            
            <select
              value={selectedStatus}
              onChange={(e) => setSelectedStatus(e.target.value)}
              className="border-2 border-gray-200 focus:border-italian-green rounded-xl px-4 py-3 h-12 bg-white shadow-sm font-medium text-gray-700"
            >
              <option value="all">All Status</option>
              <option value="published">Published</option>
              <option value="draft">Draft</option>
            </select>
            
            <Button variant="outline" className="h-12 px-6 border-2 border-gray-200 hover:border-italian-green hover:bg-italian-green hover:text-white transition-all duration-300 rounded-xl">
              <Filter className="mr-2 h-5 w-5" />
              More Filters
            </Button>
          </div>

          <Button className="h-12 px-8 bg-gradient-to-r from-italian-green to-italian-red hover:from-italian-red hover:to-italian-green transition-all duration-300 border-0 rounded-xl shadow-lg hover:shadow-xl font-semibold">
            <Plus className="mr-3 h-5 w-5" />
            New Blog Post
          </Button>
        </div>
      </div>

      {/* Blog Posts Table */}
      <Card className="bg-gradient-to-br from-white to-gray-50 border-0 shadow-xl hover:shadow-2xl transition-all duration-500">
        <CardHeader className="pb-4">
          <CardTitle className="text-2xl font-bold text-gray-900 italian-flag-corner">
            Blog Posts ({filteredPosts.length})
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b-2 border-gray-200">
                  <th className="text-left py-4 px-6 font-bold text-gray-900 bg-gradient-to-r from-gray-50 to-white">Title</th>
                  <th className="text-left py-4 px-6 font-bold text-gray-900 bg-gradient-to-r from-gray-50 to-white">Category</th>
                  <th className="text-left py-4 px-6 font-bold text-gray-900 bg-gradient-to-r from-gray-50 to-white">Author</th>
                  <th className="text-left py-4 px-6 font-bold text-gray-900 bg-gradient-to-r from-gray-50 to-white">Status</th>
                  <th className="text-left py-4 px-6 font-bold text-gray-900 bg-gradient-to-r from-gray-50 to-white">Published</th>
                  <th className="text-left py-4 px-6 font-bold text-gray-900 bg-gradient-to-r from-gray-50 to-white">Stats</th>
                  <th className="text-left py-4 px-6 font-bold text-gray-900 bg-gradient-to-r from-gray-50 to-white">Actions</th>
                </tr>
              </thead>
              <tbody>
                {filteredPosts.map((post, index) => (
                  <tr key={post.id} className="border-b border-gray-100 hover:bg-gradient-to-r hover:from-italian-green/5 hover:to-italian-red/5 transition-all duration-300 animate-fade-in" style={{ animationDelay: `${index * 50}ms` }}>
                    <td className="py-6 px-6">
                      <div>
                        <h3 className="font-bold text-gray-900 text-lg mb-2 line-clamp-1">
                          {post.title}
                        </h3>
                        <p className="text-gray-600 text-sm line-clamp-2">
                          {post.excerpt}
                        </p>
                      </div>
                    </td>
                    <td className="py-6 px-6">
                      <Badge variant="secondary" className="bg-gradient-to-r from-italian-green to-italian-red text-white border-0 px-3 py-1 font-semibold">
                        {post.category}
                      </Badge>
                    </td>
                    <td className="py-6 px-6">
                      <div className="flex items-center">
                        <div className="w-8 h-8 bg-gradient-to-br from-italian-green to-italian-red rounded-full flex items-center justify-center mr-3">
                          <User className="h-4 w-4 text-white" />
                        </div>
                        <span className="font-medium text-gray-900">{post.author}</span>
                      </div>
                    </td>
                    <td className="py-6 px-6">
                      <Badge variant={getStatusColor(post.status)} className="px-3 py-1 text-sm font-semibold">
                        {post.status}
                      </Badge>
                    </td>
                    <td className="py-6 px-6">
                      {post.publishedAt ? (
                        <div className="flex items-center text-sm text-gray-600 bg-gray-50 px-3 py-2 rounded-lg">
                          <Calendar className="h-4 w-4 mr-2 text-italian-green" />
                          {new Date(post.publishedAt).toLocaleDateString()}
                        </div>
                      ) : (
                        <span className="text-gray-400 italic">Not published</span>
                      )}
                    </td>
                    <td className="py-6 px-6">
                      <div className="space-y-1">
                        <div className="flex items-center text-sm text-gray-600">
                          <Eye className="h-4 w-4 mr-2 text-blue-600" />
                          <span className="font-medium">{post.views} views</span>
                        </div>
                        <div className="flex items-center text-sm text-gray-600">
                          <FileText className="h-4 w-4 mr-2 text-green-600" />
                          <span className="font-medium">{post.comments} comments</span>
                        </div>
                      </div>
                    </td>
                    <td className="py-6 px-6">
                      <div className="flex items-center space-x-3">
                        <Button variant="outline" size="sm" className="hover:bg-blue-500 hover:text-white transition-all duration-300 border-2">
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button variant="outline" size="sm" className="hover:bg-italian-green hover:text-white transition-all duration-300 border-2">
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button variant="outline" size="sm" className="hover:bg-italian-red hover:text-white transition-all duration-300 border-2">
                          <Trash2 className="h-4 w-4" />
                        </Button>
                        <Button variant="outline" size="sm" className="hover:bg-gray-500 hover:text-white transition-all duration-300 border-2">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>

      {/* Pagination */}
      <div className="flex justify-center">
        <div className="flex items-center space-x-3 bg-gradient-to-r from-white to-gray-50 p-4 rounded-2xl shadow-lg border-0">
          <Button variant="outline" disabled className="border-2 border-gray-200 rounded-xl px-6 py-2">
            Previous
          </Button>
          <Button variant="outline" className="border-2 border-gray-200 hover:border-italian-green hover:bg-italian-green hover:text-white transition-all duration-300 rounded-xl px-4 py-2">
            1
          </Button>
          <Button className="bg-gradient-to-r from-italian-green to-italian-red border-0 rounded-xl px-4 py-2 shadow-lg">
            2
          </Button>
          <Button variant="outline" className="border-2 border-gray-200 hover:border-italian-green hover:bg-italian-green hover:text-white transition-all duration-300 rounded-xl px-4 py-2">
            3
          </Button>
          <Button variant="outline" className="border-2 border-gray-200 hover:border-italian-green hover:bg-italian-green hover:text-white transition-all duration-300 rounded-xl px-6 py-2">
            Next
          </Button>
        </div>
      </div>
    </div>
  );
}
