import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Home,
  Key,
  Calculator,
  Users,
  Shield,
  Headphones,
  CheckCircle,
  ArrowRight
} from 'lucide-react';
import Link from 'next/link';

const services = [
  {
    icon: Home,
    title: 'Property Sales',
    description: 'Expert guidance in buying and selling residential and commercial properties across Morocco.',
    features: ['Market Analysis', 'Property Valuation', 'Negotiation Support', 'Legal Assistance'],
    price: 'Commission-based',
  },
  {
    icon: Key,
    title: 'Property Management',
    description: 'Comprehensive management services for property owners and investors.',
    features: ['Tenant Screening', 'Rent Collection', 'Maintenance Coordination', 'Financial Reporting'],
    price: 'From 5% monthly rent',
  },
  {
    icon: Calculator,
    title: 'Property Valuation',
    description: 'Professional property appraisal and market analysis services.',
    features: ['Market Research', 'Comparative Analysis', 'Investment Potential', 'Detailed Reports'],
    price: 'From 2,000 MAD',
  },
  {
    icon: Users,
    title: 'Investment Consulting',
    description: 'Strategic advice for real estate investment opportunities in Morocco.',
    features: ['Portfolio Analysis', 'ROI Calculations', 'Risk Assessment', 'Market Trends'],
    price: 'From 5,000 MAD',
  },
  {
    icon: Shield,
    title: 'Legal Support',
    description: 'Complete legal assistance for property transactions and documentation.',
    features: ['Contract Review', 'Due Diligence', 'Title Verification', 'Registration Support'],
    price: 'From 3,000 MAD',
  },
  {
    icon: Headphones,
    title: '24/7 Support',
    description: 'Round-the-clock customer support for all your real estate needs.',
    features: ['Phone Support', 'Email Assistance', 'Emergency Response', 'Multilingual Service'],
    price: 'Included',
  },
];

const processSteps = [
  {
    step: '01',
    title: 'Initial Consultation',
    description: 'We discuss your needs and requirements to understand your goals.',
  },
  {
    step: '02',
    title: 'Property Search/Analysis',
    description: 'Our experts search for properties or analyze your current portfolio.',
  },
  {
    step: '03',
    title: 'Negotiation & Documentation',
    description: 'We handle negotiations and ensure all paperwork is completed properly.',
  },
  {
    step: '04',
    title: 'Closing & Follow-up',
    description: 'We guide you through closing and provide ongoing support as needed.',
  },
];

export default function ServicesPage() {
  return (
    <div className="min-h-screen relative overflow-hidden">
      {/* Modern Background with Gradients */}
      <div className="absolute inset-0 bg-gradient-to-br from-slate-50 via-white to-emerald-50/30">
        <div className="absolute inset-0 bg-gradient-to-tr from-blue-50/20 via-transparent to-purple-50/20"></div>
      </div>

      {/* Animated Background Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-emerald-200/20 to-blue-200/20 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute -bottom-40 -left-40 w-96 h-96 bg-gradient-to-tr from-purple-200/15 to-pink-200/15 rounded-full blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute top-1/3 right-1/4 w-64 h-64 bg-gradient-to-r from-yellow-200/10 to-orange-200/10 rounded-full blur-2xl animate-float-slow"></div>
      </div>

      {/* Decorative Grid Pattern */}
      <div className="absolute inset-0 opacity-[0.02]">
        <div className="absolute inset-0" style={{
          backgroundImage: `radial-gradient(circle at 1px 1px, rgb(59 130 246) 1px, transparent 0)`,
          backgroundSize: '40px 40px'
        }}></div>
      </div>

      <div className="relative py-16">
        <div className="container mx-auto px-4">
          {/* Modern Hero Section */}
          <div className="text-center mb-20">
            <div className="inline-flex items-center gap-2 bg-white/80 backdrop-blur-sm border border-emerald-200/50 rounded-full px-6 py-2 mb-8 animate-fade-in">
              <div className="w-2 h-2 bg-emerald-400 rounded-full animate-pulse"></div>
              <span className="text-emerald-700 font-medium">Our Services</span>
            </div>

            <h1 className="text-5xl lg:text-7xl font-bold mb-8 animate-slide-up">
              <span className="bg-gradient-to-r from-gray-900 via-emerald-700 to-blue-700 bg-clip-text text-transparent">
                Comprehensive
              </span>
              <br />
              <span className="bg-gradient-to-r from-emerald-500 via-blue-500 to-purple-500 bg-clip-text text-transparent">
                Real Estate Services
              </span>
            </h1>

            <p className="text-xl lg:text-2xl text-gray-600 max-w-4xl mx-auto leading-relaxed animate-slide-up-delayed">
              From property sales to management, we provide end-to-end real estate solutions
              tailored to the Moroccan market. Your success is our priority, and excellence is our standard.
            </p>
          </div>

          {/* Modern Services Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-24">
            {services.map((service, index) => {
              const Icon = service.icon;
              const gradients = [
                'from-emerald-500 to-teal-500',
                'from-blue-500 to-indigo-500',
                'from-purple-500 to-pink-500',
                'from-orange-500 to-red-500',
                'from-cyan-500 to-blue-500',
                'from-green-500 to-emerald-500'
              ];
              const bgGradients = [
                'from-emerald-50 to-teal-50',
                'from-blue-50 to-indigo-50',
                'from-purple-50 to-pink-50',
                'from-orange-50 to-red-50',
                'from-cyan-50 to-blue-50',
                'from-green-50 to-emerald-50'
              ];

              return (
                <Card
                  key={index}
                  className="group h-full bg-white/80 backdrop-blur-sm border-0 shadow-xl hover:shadow-2xl transition-all duration-500 transform hover:scale-[1.02] hover:-translate-y-2 rounded-3xl overflow-hidden animate-fade-in"
                  style={{ animationDelay: `${index * 0.1}s` }}
                >
                  <CardHeader className={`bg-gradient-to-br ${bgGradients[index % bgGradients.length]} border-b border-gray-100/50 pb-6`}>
                    <div className={`w-16 h-16 bg-gradient-to-br ${gradients[index % gradients.length]} rounded-2xl flex items-center justify-center mb-6 shadow-lg group-hover:scale-110 transition-transform duration-300`}>
                      <Icon className="h-8 w-8 text-white" />
                    </div>
                    <CardTitle className="text-2xl font-bold text-gray-900 mb-2 group-hover:text-emerald-600 transition-colors duration-300">
                      {service.title}
                    </CardTitle>
                    <div className={`inline-flex items-center bg-gradient-to-r ${gradients[index % gradients.length]} text-white px-4 py-2 rounded-xl font-bold shadow-md`}>
                      {service.price}
                    </div>
                  </CardHeader>
                  <CardContent className="p-8">
                    <p className="text-gray-700 mb-6 leading-relaxed text-lg">{service.description}</p>
                    <ul className="space-y-3 mb-8">
                      {service.features.map((feature, idx) => (
                        <li key={idx} className="flex items-center">
                          <div className={`flex items-center justify-center w-6 h-6 bg-gradient-to-br ${gradients[index % gradients.length]} rounded-full mr-3 shadow-sm`}>
                            <CheckCircle className="h-3 w-3 text-white" />
                          </div>
                          <span className="text-gray-700 font-medium">{feature}</span>
                        </li>
                      ))}
                    </ul>
                    <Button className={`w-full bg-gradient-to-r ${gradients[index % gradients.length]} hover:shadow-xl text-white border-0 rounded-xl py-3 font-semibold transition-all duration-300 transform hover:scale-105 group`}>
                      <span>Learn More</span>
                      <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform duration-300" />
                    </Button>
                  </CardContent>
                </Card>
              );
            })}
          </div>

          {/* Modern Process Section */}
          <div className="bg-white/80 backdrop-blur-sm rounded-3xl p-8 lg:p-16 mb-24 shadow-xl border border-gray-200/50">
            <div className="text-center mb-16">
              <div className="inline-flex items-center gap-2 bg-gradient-to-r from-emerald-100 to-blue-100 rounded-full px-6 py-2 mb-6">
                <div className="w-2 h-2 bg-gradient-to-r from-emerald-500 to-blue-500 rounded-full animate-pulse"></div>
                <span className="text-emerald-700 font-medium">Our Process</span>
              </div>

              <h2 className="text-4xl lg:text-5xl font-bold mb-6">
                <span className="bg-gradient-to-r from-gray-900 via-emerald-700 to-blue-700 bg-clip-text text-transparent">
                  How We Work
                </span>
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
                We follow a proven process to ensure the best outcomes for our clients.
                Every step is designed to provide transparency, efficiency, and complete peace of mind.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              {processSteps.map((step, index) => (
                <div key={index} className="text-center group">
                  <div className="relative mb-6">
                    <div className="w-20 h-20 bg-gradient-to-br from-emerald-400 to-blue-500 rounded-2xl flex items-center justify-center text-white font-bold text-2xl mx-auto shadow-lg group-hover:scale-110 transition-transform duration-300">
                      {step.step}
                    </div>
                    {index < processSteps.length - 1 && (
                      <div className="hidden lg:block absolute top-10 left-full w-full h-0.5 bg-gradient-to-r from-emerald-200 to-blue-200 transform translate-x-4"></div>
                    )}
                  </div>
                  <h3 className="text-xl font-bold text-gray-900 mb-3 group-hover:text-emerald-600 transition-colors duration-300">
                    {step.title}
                  </h3>
                  <p className="text-gray-600 leading-relaxed">
                    {step.description}
                  </p>
                </div>
              ))}
            </div>
          </div>

          {/* Modern CTA Section */}
          <div className="relative text-center bg-gradient-to-br from-emerald-500 via-blue-500 to-purple-500 rounded-3xl p-8 lg:p-16 text-white overflow-hidden shadow-2xl">
            {/* Background Pattern */}
            <div className="absolute inset-0 opacity-10">
              <div className="absolute inset-0" style={{
                backgroundImage: `radial-gradient(circle at 1px 1px, white 1px, transparent 0)`,
                backgroundSize: '30px 30px'
              }}></div>
            </div>

            {/* Floating Elements */}
            <div className="absolute top-4 left-4 w-8 h-8 bg-white/20 rounded-full animate-float"></div>
            <div className="absolute top-8 right-8 w-6 h-6 bg-white/20 rounded-full animate-float-delayed"></div>
            <div className="absolute bottom-8 left-8 w-4 h-4 bg-white/20 rounded-full animate-float-slow"></div>

            <div className="relative">
              <h2 className="text-4xl lg:text-6xl font-bold mb-6">
                Ready to Get Started?
              </h2>
              <p className="text-xl lg:text-2xl mb-10 opacity-90 max-w-3xl mx-auto leading-relaxed">
                Contact us today for a free consultation and discover how we can help
                you achieve your real estate goals with our expert guidance and personalized service.
              </p>
              <div className="flex flex-col sm:flex-row gap-6 justify-center">
                <Button size="lg" className="bg-white text-emerald-600 hover:bg-gray-100 px-8 py-4 rounded-xl font-semibold shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105" asChild>
                  <Link href="/contact">
                    Get Free Consultation
                  </Link>
                </Button>
                <Button size="lg" className="bg-white/20 backdrop-blur-sm border-white/30 text-white hover:bg-white hover:text-emerald-600 px-8 py-4 rounded-xl font-semibold transition-all duration-300 transform hover:scale-105" asChild>
                  <Link href="/properties">
                    Browse Properties
                  </Link>
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
