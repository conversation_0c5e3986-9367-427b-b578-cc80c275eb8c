// Environment configuration for production readiness

export const config = {
  // App Configuration
  app: {
    name: process.env.NEXT_PUBLIC_APP_NAME || 'Darden Property & Management',
    url: process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000',
    environment: process.env.NODE_ENV || 'development',
  },

  // Supabase Configuration
  supabase: {
    url: process.env.NEXT_PUBLIC_SUPABASE_URL!,
    anonKey: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    serviceRoleKey: process.env.SUPABASE_SERVICE_ROLE_KEY,
    storageBucket: process.env.NEXT_PUBLIC_SUPABASE_STORAGE_BUCKET || 'property-images',
  },

  // Authentication
  auth: {
    secret: process.env.NEXTAUTH_SECRET,
    url: process.env.NEXTAUTH_URL || process.env.NEXT_PUBLIC_APP_URL,
  },

  // Email Configuration
  email: {
    host: process.env.SMTP_HOST,
    port: parseInt(process.env.SMTP_PORT || '587'),
    user: process.env.SMTP_USER,
    password: process.env.SMTP_PASS,
    from: process.env.SMTP_FROM || '<EMAIL>',
  },

  // File Upload Configuration
  upload: {
    maxFileSize: parseInt(process.env.NEXT_PUBLIC_MAX_FILE_SIZE || '5242880'), // 5MB
    allowedTypes: (process.env.NEXT_PUBLIC_ALLOWED_FILE_TYPES || 'image/jpeg,image/png,image/webp').split(','),
  },

  // Analytics
  analytics: {
    googleAnalyticsId: process.env.NEXT_PUBLIC_GA_ID,
    hotjarId: process.env.NEXT_PUBLIC_HOTJAR_ID,
    enabled: process.env.NEXT_PUBLIC_ENABLE_ANALYTICS === 'true',
  },

  // Rate Limiting
  rateLimit: {
    max: parseInt(process.env.RATE_LIMIT_MAX || '100'),
    windowMs: parseInt(process.env.RATE_LIMIT_WINDOW || '900000'), // 15 minutes
  },

  // Logging
  logging: {
    level: process.env.LOG_LEVEL || 'info',
    sentryDsn: process.env.SENTRY_DSN,
  },

  // Feature Flags
  features: {
    analytics: process.env.NEXT_PUBLIC_ENABLE_ANALYTICS === 'true',
    chat: process.env.NEXT_PUBLIC_ENABLE_CHAT === 'true',
    virtualTours: process.env.NEXT_PUBLIC_ENABLE_VIRTUAL_TOURS === 'true',
  },

  // Production checks
  isProduction: process.env.NODE_ENV === 'production',
  isDevelopment: process.env.NODE_ENV === 'development',
  isTest: process.env.NODE_ENV === 'test',
} as const;

// Validation function to ensure required environment variables are set
export function validateConfig() {
  const requiredVars = [
    'NEXT_PUBLIC_SUPABASE_URL',
    'NEXT_PUBLIC_SUPABASE_ANON_KEY',
  ];

  const missingVars = requiredVars.filter(varName => !process.env[varName]);

  if (missingVars.length > 0) {
    throw new Error(
      `Missing required environment variables: ${missingVars.join(', ')}\n` +
      'Please check your .env file and ensure all required variables are set.'
    );
  }

  // Production-specific validations
  if (config.isProduction) {
    const productionRequiredVars = [
      'NEXTAUTH_SECRET',
      'SUPABASE_SERVICE_ROLE_KEY',
    ];

    const missingProdVars = productionRequiredVars.filter(varName => !process.env[varName]);

    if (missingProdVars.length > 0) {
      console.warn(
        `Missing recommended production environment variables: ${missingProdVars.join(', ')}`
      );
    }

    // Validate URLs in production
    try {
      new URL(config.app.url);
      new URL(config.supabase.url);
    } catch {
      throw new Error('Invalid URL configuration detected in production environment');
    }
  }
}

// Database configuration
export const dbConfig = {
  // Connection pool settings for production
  pool: {
    min: 2,
    max: 10,
    acquireTimeoutMillis: 30000,
    createTimeoutMillis: 30000,
    destroyTimeoutMillis: 5000,
    idleTimeoutMillis: 30000,
    reapIntervalMillis: 1000,
    createRetryIntervalMillis: 200,
  },
};

// Security configuration
export const securityConfig = {
  // CORS settings
  cors: {
    origin: config.isProduction 
      ? [config.app.url, 'https://dardenpm.com'] 
      : true,
    credentials: true,
  },

  // Content Security Policy
  csp: {
    directives: {
      defaultSrc: ["'self'"],
      scriptSrc: ["'self'", "'unsafe-eval'", "'unsafe-inline'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      imgSrc: ["'self'", "data:", "https:"],
      connectSrc: ["'self'", config.supabase.url],
      fontSrc: ["'self'"],
      objectSrc: ["'none'"],
      mediaSrc: ["'self'"],
      frameSrc: ["'none'"],
    },
  },

  // Rate limiting by endpoint
  rateLimits: {
    auth: { max: 5, windowMs: 15 * 60 * 1000 }, // 5 attempts per 15 minutes
    api: { max: 100, windowMs: 15 * 60 * 1000 }, // 100 requests per 15 minutes
    upload: { max: 10, windowMs: 60 * 1000 }, // 10 uploads per minute
  },
};

// Performance configuration
export const performanceConfig = {
  // Image optimization
  images: {
    domains: [
      new URL(config.supabase.url).hostname,
      'images.unsplash.com',
      'via.placeholder.com',
    ],
    formats: ['image/webp', 'image/avif'],
    sizes: [16, 32, 48, 64, 96, 128, 256, 384],
  },

  // Caching
  cache: {
    staticAssets: '1y',
    apiResponses: '5m',
    images: '1M',
  },
};

// Monitoring configuration
export const monitoringConfig = {
  // Health check endpoints
  healthChecks: {
    database: '/api/health/database',
    storage: '/api/health/storage',
    external: '/api/health/external',
  },

  // Metrics collection
  metrics: {
    enabled: config.isProduction,
    interval: 60000, // 1 minute
  },

  // Error tracking
  errorTracking: {
    enabled: config.isProduction && !!config.logging.sentryDsn,
    sampleRate: 0.1, // 10% of errors
  },
};

// Initialize configuration validation on import
if (typeof window === 'undefined') {
  // Only validate on server side
  try {
    validateConfig();
  } catch (error) {
    console.error('Configuration validation failed:', error);
    if (config.isProduction) {
      process.exit(1);
    }
  }
}

export default config;
