import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { MessageSquare, Clock, User } from 'lucide-react';

const inquiries = [
  {
    id: '1',
    name: '<PERSON>',
    property: 'Luxury Villa in Casablanca',
    type: 'purchase',
    status: 'new',
    time: '2 hours ago',
  },
  {
    id: '2',
    name: '<PERSON>',
    property: 'Modern Apartment in Rabat',
    type: 'rent',
    status: 'contacted',
    time: '5 hours ago',
  },
  {
    id: '3',
    name: '<PERSON>',
    property: 'Traditional Riad in Marrakech',
    type: 'information',
    status: 'scheduled',
    time: '1 day ago',
  },
];

export function RecentInquiries() {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <MessageSquare className="mr-2 h-5 w-5" />
          Recent Inquiries
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {inquiries.map((inquiry) => (
            <div key={inquiry.id} className="flex items-center justify-between p-4 border rounded-lg">
              <div className="flex items-center space-x-4">
                <div className="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center">
                  <User className="h-5 w-5 text-gray-600" />
                </div>
                <div>
                  <div className="font-medium">{inquiry.name}</div>
                  <div className="text-sm text-gray-600">{inquiry.property}</div>
                  <div className="flex items-center space-x-2 mt-1">
                    <Badge variant="secondary">{inquiry.type}</Badge>
                    <Badge variant={inquiry.status === 'new' ? 'info' : 'success'}>
                      {inquiry.status}
                    </Badge>
                  </div>
                </div>
              </div>
              <div className="text-right">
                <div className="flex items-center text-sm text-gray-500 mb-2">
                  <Clock className="mr-1 h-4 w-4" />
                  {inquiry.time}
                </div>
                <Button size="sm">Respond</Button>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
