'use client';

import { useState, useEffect } from 'react';
import { PropertySearch, type SearchFilters } from '@/components/features/properties/property-search';
import { PropertyGrid } from '@/components/features/properties/property-grid';
import { useProperties } from '@/hooks/use-properties';

export default function PropertiesPage() {
  const [searchFilters, setSearchFilters] = useState<SearchFilters>({});

  const {
    properties,
    loading,
    error,
    totalCount
  } = useProperties({
    ...searchFilters,
    limit: 12
  });



  const handleSearch = (filters: SearchFilters) => {
    setSearchFilters(filters);
  };

  const handleReset = () => {
    setSearchFilters({});
  };

  return (
    <div className="min-h-screen relative overflow-hidden">
      {/* Modern Background with Gradients */}
      <div className="absolute inset-0 bg-gradient-to-br from-slate-50 via-white to-blue-50/30">
        <div className="absolute inset-0 bg-gradient-to-tr from-emerald-50/20 via-transparent to-purple-50/20"></div>
      </div>

      {/* Animated Background Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-emerald-200/20 to-blue-200/20 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute -bottom-40 -left-40 w-96 h-96 bg-gradient-to-tr from-purple-200/15 to-pink-200/15 rounded-full blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute top-1/3 right-1/4 w-64 h-64 bg-gradient-to-r from-yellow-200/10 to-orange-200/10 rounded-full blur-2xl animate-float-slow"></div>
      </div>

      {/* Decorative Grid Pattern */}
      <div className="absolute inset-0 opacity-[0.02]">
        <div className="absolute inset-0" style={{
          backgroundImage: `radial-gradient(circle at 1px 1px, rgb(59 130 246) 1px, transparent 0)`,
          backgroundSize: '40px 40px'
        }}></div>
      </div>

      <div className="relative container mx-auto px-4 py-12">
        <div className="mb-12 text-center">
          <h1 className="text-4xl lg:text-5xl font-bold mb-6 animate-slide-up">
            <span className="bg-gradient-to-r from-gray-900 via-emerald-700 to-blue-700 bg-clip-text text-transparent">
              Find Your Perfect Property
            </span>
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed animate-slide-up-delayed">
            Discover exceptional properties across Morocco. From luxury villas in Casablanca
            to traditional riads in Marrakech, find your dream home with modern amenities and timeless elegance.
          </p>
        </div>

        <div className="animate-slide-up-delayed-2">
          <PropertySearch onSearch={handleSearch} onReset={handleReset} />
        </div>

        <div className="mt-12 animate-slide-up-delayed-3">
          <PropertyGrid
            properties={properties}
            loading={loading}
            error={error}
            totalCount={totalCount}
          />
        </div>
      </div>
    </div>
  );
}
