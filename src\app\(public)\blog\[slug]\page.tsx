import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  Calendar, 
  User, 
  Clock, 
  Share2, 
  Heart, 
  MessageCircle,
  ArrowLeft,
  ArrowRight,
  Eye
} from 'lucide-react';
import Link from 'next/link';

interface BlogPostPageProps {
  params: Promise<{
    slug: string;
  }>;
}

// Mock blog post data - will be replaced with real data from Supabase
const mockBlogPost = {
  id: '1',
  title: 'Morocco Real Estate Market Trends 2024',
  slug: 'morocco-real-estate-trends-2024',
  content: `
    <p>Morocco's real estate market continues to show remarkable resilience and growth in 2024, driven by several key factors including international sporting events, government initiatives, and increasing foreign investment.</p>
    
    <h2>Key Market Drivers</h2>
    <p>The upcoming FIFA World Cup 2030 and CAF 2025 have significantly boosted investor confidence in Morocco's real estate sector. These events are driving infrastructure development and creating new opportunities across major cities.</p>
    
    <h3>Price Trends</h3>
    <p>Property prices in major cities like Casablanca and Marrakech have seen steady growth, with luxury properties experiencing the highest appreciation rates. The average price per square meter has increased by 8-12% year-over-year.</p>
    
    <h3>Investment Hotspots</h3>
    <p>Several neighborhoods have emerged as prime investment destinations:</p>
    <ul>
      <li>Casablanca Finance City - Commercial and residential developments</li>
      <li>Marrakech Gueliz - Luxury residential properties</li>
      <li>Rabat Hay Riad - Government and diplomatic quarter</li>
      <li>Tangier Marina - Coastal luxury developments</li>
    </ul>
    
    <h2>Foreign Investment Trends</h2>
    <p>Foreign buyers, particularly from Europe and the Gulf states, continue to show strong interest in Moroccan real estate. The government's investor-friendly policies and simplified procedures have made property acquisition more accessible.</p>
    
    <h3>Legal Framework</h3>
    <p>Recent updates to property laws have streamlined the buying process for foreign investors, while maintaining necessary protections for local markets. These changes have contributed to increased transparency and confidence in the market.</p>
    
    <h2>Future Outlook</h2>
    <p>Looking ahead, the Moroccan real estate market is positioned for continued growth, supported by:</p>
    <ul>
      <li>Infrastructure investments related to international events</li>
      <li>Growing tourism sector</li>
      <li>Expanding middle class</li>
      <li>Government housing initiatives</li>
    </ul>
    
    <p>Investors and buyers should consider these trends when making real estate decisions in Morocco's dynamic market.</p>
  `,
  excerpt: 'Discover the latest trends shaping Morocco\'s real estate market in 2024, including price movements and investment opportunities.',
  category: 'Market Analysis',
  author: 'Ahmed Benali',
  publishedAt: '2024-01-15',
  readTime: '5 min read',
  image: '/blog/market-trends.jpg',
  views: 1234,
  likes: 89,
  comments: 23,
  tags: ['Morocco', 'Real Estate', 'Market Trends', 'Investment', 'FIFA 2030']
};

const relatedPosts = [
  {
    id: '2',
    title: 'FIFA World Cup 2030: Impact on Moroccan Property Market',
    slug: 'fifa-2030-morocco-property-impact',
    category: 'Investment',
    readTime: '7 min read',
    publishedAt: '2024-01-10'
  },
  {
    id: '3',
    title: 'Best Neighborhoods to Invest in Casablanca',
    slug: 'best-neighborhoods-casablanca-investment',
    category: 'Investment Guide',
    readTime: '6 min read',
    publishedAt: '2024-01-05'
  }
];

export default async function BlogPostPage({ params }: BlogPostPageProps) {
  const { slug } = await params;
  
  // In a real app, you would fetch the blog post by slug from Supabase
  // For now, we'll use mock data
  const post = mockBlogPost;

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-100">
      {/* Hero Section */}
      <div className="relative bg-gradient-to-br from-italian-green/10 via-white to-italian-red/10 py-20">
        <div className="absolute top-0 left-0 w-full h-2 bg-gradient-to-r from-italian-green via-white to-italian-red"></div>
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="mb-8">
              <Link href="/blog" className="inline-flex items-center text-italian-green hover:text-italian-red transition-colors duration-300 font-medium">
                <ArrowLeft className="mr-2 h-5 w-5" />
                Back to Blog
              </Link>
            </div>
            
            <div className="text-center">
              <Badge variant="secondary" className="mb-6 bg-gradient-to-r from-italian-green to-italian-red text-white border-0 px-6 py-2 font-medium animate-fade-in">
                {post.category}
              </Badge>
              
              <h1 className="text-4xl lg:text-6xl font-bold text-gray-900 mb-8 italian-flag-corner animate-slide-up leading-tight">
                {post.title}
              </h1>
              
              <div className="flex flex-wrap items-center justify-center gap-6 text-gray-600 mb-8 animate-fade-in-delay">
                <div className="flex items-center bg-white px-4 py-2 rounded-full shadow-sm">
                  <User className="h-5 w-5 mr-2 text-italian-green" />
                  <span className="font-medium">{post.author}</span>
                </div>
                <div className="flex items-center bg-white px-4 py-2 rounded-full shadow-sm">
                  <Calendar className="h-5 w-5 mr-2 text-italian-green" />
                  <span className="font-medium">{new Date(post.publishedAt).toLocaleDateString()}</span>
                </div>
                <div className="flex items-center bg-white px-4 py-2 rounded-full shadow-sm">
                  <Clock className="h-5 w-5 mr-2 text-italian-green" />
                  <span className="font-medium">{post.readTime}</span>
                </div>
                <div className="flex items-center bg-white px-4 py-2 rounded-full shadow-sm">
                  <Eye className="h-5 w-5 mr-2 text-italian-green" />
                  <span className="font-medium">{post.views} views</span>
                </div>
              </div>
              
              <div className="flex items-center justify-center gap-4 animate-scale-in">
                <Button variant="outline" className="border-2 border-italian-green text-italian-green hover:bg-italian-green hover:text-white transition-all duration-300">
                  <Heart className="mr-2 h-5 w-5" />
                  {post.likes}
                </Button>
                <Button variant="outline" className="border-2 border-italian-red text-italian-red hover:bg-italian-red hover:text-white transition-all duration-300">
                  <MessageCircle className="mr-2 h-5 w-5" />
                  {post.comments}
                </Button>
                <Button variant="outline" className="border-2 border-gray-300 hover:border-italian-green hover:bg-italian-green hover:text-white transition-all duration-300">
                  <Share2 className="mr-2 h-5 w-5" />
                  Share
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Featured Image */}
      <div className="container mx-auto px-4 -mt-10 relative z-10">
        <div className="max-w-4xl mx-auto">
          <div className="aspect-[16/9] bg-gradient-to-br from-gray-200 to-gray-300 rounded-2xl shadow-2xl overflow-hidden animate-scale-in">
            <div className="w-full h-full bg-gradient-to-br from-italian-green/20 to-italian-red/20 flex items-center justify-center">
              <Eye className="h-24 w-24 text-gray-400" />
            </div>
          </div>
        </div>
      </div>

      {/* Article Content */}
      <div className="container mx-auto px-4 py-16">
        <div className="max-w-4xl mx-auto">
          <Card className="bg-gradient-to-br from-white to-gray-50 border-0 shadow-xl">
            <CardContent className="p-12">
              <div 
                className="prose prose-lg max-w-none prose-headings:text-gray-900 prose-headings:font-bold prose-h2:text-3xl prose-h2:mb-6 prose-h2:mt-12 prose-h3:text-2xl prose-h3:mb-4 prose-h3:mt-8 prose-p:text-gray-700 prose-p:leading-relaxed prose-p:mb-6 prose-ul:mb-6 prose-li:mb-2 prose-strong:text-gray-900"
                dangerouslySetInnerHTML={{ __html: post.content }}
              />
              
              {/* Tags */}
              <div className="mt-12 pt-8 border-t border-gray-200">
                <h3 className="text-lg font-bold text-gray-900 mb-4">Tags</h3>
                <div className="flex flex-wrap gap-3">
                  {post.tags.map((tag, index) => (
                    <Badge 
                      key={tag} 
                      variant="outline" 
                      className="border-2 border-gray-200 hover:border-italian-green hover:bg-italian-green hover:text-white transition-all duration-300 px-3 py-1 animate-scale-in"
                      style={{ animationDelay: `${index * 100}ms` }}
                    >
                      {tag}
                    </Badge>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Related Posts */}
      <div className="container mx-auto px-4 pb-16">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold text-gray-900 mb-8 italian-flag-corner">Related Articles</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {relatedPosts.map((relatedPost, index) => (
              <Card key={relatedPost.id} className="overflow-hidden hover:shadow-xl transition-all duration-500 hover-lift group bg-gradient-to-br from-white to-gray-50 border-0 shadow-lg animate-scale-in" style={{ animationDelay: `${index * 200}ms` }}>
                <div className="aspect-[16/10] bg-gradient-to-br from-gray-200 to-gray-300 relative">
                  <div className="absolute inset-0 bg-gradient-to-br from-italian-green/10 to-italian-red/10"></div>
                  <div className="absolute inset-0 flex items-center justify-center text-gray-400">
                    <Eye className="h-12 w-12" />
                  </div>
                  <div className="absolute top-4 left-4">
                    <Badge variant="secondary" className="bg-gradient-to-r from-italian-green to-italian-red text-white border-0">
                      {relatedPost.category}
                    </Badge>
                  </div>
                </div>
                <CardContent className="p-6">
                  <h3 className="text-xl font-bold text-gray-900 mb-4 line-clamp-2 group-hover:text-italian-green transition-colors duration-300">
                    {relatedPost.title}
                  </h3>
                  <div className="flex items-center justify-between text-sm text-gray-600 mb-4">
                    <span>{new Date(relatedPost.publishedAt).toLocaleDateString()}</span>
                    <span className="font-medium">{relatedPost.readTime}</span>
                  </div>
                  <Button asChild className="w-full bg-gradient-to-r from-italian-green to-italian-red hover:from-italian-red hover:to-italian-green transition-all duration-300 border-0">
                    <Link href={`/blog/${relatedPost.slug}`}>
                      Read More
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </Link>
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
