import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Plus, Upload, FileText, Users } from 'lucide-react';
import Link from 'next/link';

const actions = [
  {
    title: 'Add Property',
    description: 'List a new property',
    icon: Plus,
    href: '/dashboard/properties/new',
    color: 'bg-blue-500 hover:bg-blue-600',
  },
  {
    title: 'Upload Images',
    description: 'Add property photos',
    icon: Upload,
    href: '/dashboard/media',
    color: 'bg-green-500 hover:bg-green-600',
  },
  {
    title: 'Create Blog Post',
    description: 'Write a new article',
    icon: FileText,
    href: '/admin/blog/new',
    color: 'bg-purple-500 hover:bg-purple-600',
  },
  {
    title: 'Manage Users',
    description: 'User administration',
    icon: Users,
    href: '/admin/users',
    color: 'bg-orange-500 hover:bg-orange-600',
  },
];

export function QuickActions() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Quick Actions</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          {actions.map((action, index) => {
            const Icon = action.icon;
            return (
              <Button
                key={index}
                asChild
                variant="outline"
                className="w-full justify-start h-auto p-4"
              >
                <Link href={action.href}>
                  <div className={`w-10 h-10 rounded-lg flex items-center justify-center mr-3 ${action.color}`}>
                    <Icon className="h-5 w-5 text-white" />
                  </div>
                  <div className="text-left">
                    <div className="font-medium">{action.title}</div>
                    <div className="text-sm text-gray-600">{action.description}</div>
                  </div>
                </Link>
              </Button>
            );
          })}
        </div>
      </CardContent>
    </Card>
  );
}
