import { PropertyList } from '@/components/features/properties/property-list';
import { PropertyStats } from '@/components/features/properties/property-stats';
import { Button } from '@/components/ui/button';
import { Plus } from 'lucide-react';
import Link from 'next/link';

export default function PropertiesPage() {
  return (
    <div className="space-y-8">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 italian-flag-corner">Properties</h1>
          <p className="text-gray-600">Manage your property listings and track performance.</p>
        </div>
        <Button asChild>
          <Link href="/dashboard/properties/new">
            <Plus className="mr-2 h-4 w-4" />
            Add Property
          </Link>
        </Button>
      </div>

      <PropertyStats />
      <PropertyList />
    </div>
  );
}
