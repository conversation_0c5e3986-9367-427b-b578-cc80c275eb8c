'use client';

import { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { 
  Save, 
  Eye, 
  Upload, 
  X,
  Plus,
  ArrowLeft
} from 'lucide-react';
import Link from 'next/link';

const categories = [
  'Market Analysis',
  'Investment',
  'Investment Guide',
  'Legal Guide',
  'Property Tips',
  'News',
  'Trends'
];

export function BlogEditor() {
  const [title, setTitle] = useState('');
  const [slug, setSlug] = useState('');
  const [excerpt, setExcerpt] = useState('');
  const [content, setContent] = useState('');
  const [category, setCategory] = useState('');
  const [tags, setTags] = useState<string[]>([]);
  const [newTag, setNewTag] = useState('');
  const [status, setStatus] = useState('draft');

  const generateSlug = (title: string) => {
    return title
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/(^-|-$)/g, '');
  };

  const handleTitleChange = (value: string) => {
    setTitle(value);
    if (!slug || slug === generateSlug(title)) {
      setSlug(generateSlug(value));
    }
  };

  const addTag = () => {
    if (newTag.trim() && !tags.includes(newTag.trim())) {
      setTags([...tags, newTag.trim()]);
      setNewTag('');
    }
  };

  const removeTag = (tagToRemove: string) => {
    setTags(tags.filter(tag => tag !== tagToRemove));
  };

  const handleSave = (saveStatus: string) => {
    // Here you would save the blog post to Supabase
    console.log('Saving blog post:', {
      title,
      slug,
      excerpt,
      content,
      category,
      tags,
      status: saveStatus
    });
  };

  return (
    <div className="space-y-8">
      {/* Header Actions */}
      <div className="bg-gradient-to-r from-white to-gray-50 rounded-2xl p-6 shadow-lg border-0">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
          <div className="flex items-center space-x-4">
            <Link href="/admin/blog" className="inline-flex items-center text-italian-green hover:text-italian-red transition-colors duration-300 font-medium">
              <ArrowLeft className="mr-2 h-5 w-5" />
              Back to Blog Management
            </Link>
          </div>

          <div className="flex items-center space-x-4">
            <Button variant="outline" className="border-2 border-gray-200 hover:border-italian-green hover:bg-italian-green hover:text-white transition-all duration-300 rounded-xl">
              <Eye className="mr-2 h-5 w-5" />
              Preview
            </Button>
            <Button 
              variant="outline" 
              onClick={() => handleSave('draft')}
              className="border-2 border-gray-200 hover:border-italian-green hover:bg-italian-green hover:text-white transition-all duration-300 rounded-xl"
            >
              <Save className="mr-2 h-5 w-5" />
              Save Draft
            </Button>
            <Button 
              onClick={() => handleSave('published')}
              className="bg-gradient-to-r from-italian-green to-italian-red hover:from-italian-red hover:to-italian-green transition-all duration-300 border-0 rounded-xl shadow-lg hover:shadow-xl font-semibold"
            >
              Publish
            </Button>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-8">
          {/* Basic Information */}
          <Card className="bg-gradient-to-br from-white to-gray-50 border-0 shadow-xl hover:shadow-2xl transition-all duration-500">
            <CardHeader className="pb-4">
              <CardTitle className="text-2xl font-bold text-gray-900 italian-flag-corner">
                Basic Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div>
                <label className="block text-sm font-bold text-gray-900 mb-2">
                  Title *
                </label>
                <Input
                  value={title}
                  onChange={(e) => handleTitleChange(e.target.value)}
                  placeholder="Enter blog post title..."
                  className="h-12 border-2 border-gray-200 focus:border-italian-green rounded-xl bg-white shadow-sm"
                />
              </div>

              <div>
                <label className="block text-sm font-bold text-gray-900 mb-2">
                  Slug
                </label>
                <Input
                  value={slug}
                  onChange={(e) => setSlug(e.target.value)}
                  placeholder="blog-post-url-slug"
                  className="h-12 border-2 border-gray-200 focus:border-italian-green rounded-xl bg-white shadow-sm"
                />
                <p className="text-sm text-gray-500 mt-1">
                  URL: /blog/{slug || 'your-slug-here'}
                </p>
              </div>

              <div>
                <label className="block text-sm font-bold text-gray-900 mb-2">
                  Excerpt *
                </label>
                <Textarea
                  value={excerpt}
                  onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => setExcerpt(e.target.value)}
                  placeholder="Brief description of the blog post..."
                  rows={3}
                  className="border-2 border-gray-200 focus:border-italian-green rounded-xl bg-white shadow-sm resize-none"
                />
              </div>
            </CardContent>
          </Card>

          {/* Content Editor */}
          <Card className="bg-gradient-to-br from-white to-gray-50 border-0 shadow-xl hover:shadow-2xl transition-all duration-500">
            <CardHeader className="pb-4">
              <CardTitle className="text-2xl font-bold text-gray-900 italian-flag-corner">
                Content
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div>
                <label className="block text-sm font-bold text-gray-900 mb-2">
                  Blog Content *
                </label>
                <Textarea
                  value={content}
                  onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => setContent(e.target.value)}
                  placeholder="Write your blog post content here..."
                  rows={20}
                  className="border-2 border-gray-200 focus:border-italian-green rounded-xl bg-white shadow-sm resize-none"
                />
                <p className="text-sm text-gray-500 mt-2">
                  You can use HTML tags for formatting. A rich text editor will be added in future updates.
                </p>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-8">
          {/* Featured Image */}
          <Card className="bg-gradient-to-br from-white to-gray-50 border-0 shadow-xl hover:shadow-2xl transition-all duration-500">
            <CardHeader className="pb-4">
              <CardTitle className="text-xl font-bold text-gray-900 italian-flag-corner">
                Featured Image
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="border-2 border-dashed border-gray-300 rounded-xl p-8 text-center hover:border-italian-green transition-colors duration-300">
                <Upload className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-600 mb-4">
                  Click to upload or drag and drop
                </p>
                <Button variant="outline" className="border-2 border-gray-200 hover:border-italian-green hover:bg-italian-green hover:text-white transition-all duration-300">
                  Choose File
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Category */}
          <Card className="bg-gradient-to-br from-white to-gray-50 border-0 shadow-xl hover:shadow-2xl transition-all duration-500">
            <CardHeader className="pb-4">
              <CardTitle className="text-xl font-bold text-gray-900 italian-flag-corner">
                Category
              </CardTitle>
            </CardHeader>
            <CardContent>
              <select
                value={category}
                onChange={(e) => setCategory(e.target.value)}
                className="w-full border-2 border-gray-200 focus:border-italian-green rounded-xl px-4 py-3 bg-white shadow-sm font-medium text-gray-700"
              >
                <option value="">Select Category</option>
                {categories.map((cat) => (
                  <option key={cat} value={cat}>
                    {cat}
                  </option>
                ))}
              </select>
            </CardContent>
          </Card>

          {/* Tags */}
          <Card className="bg-gradient-to-br from-white to-gray-50 border-0 shadow-xl hover:shadow-2xl transition-all duration-500">
            <CardHeader className="pb-4">
              <CardTitle className="text-xl font-bold text-gray-900 italian-flag-corner">
                Tags
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex space-x-2">
                <Input
                  value={newTag}
                  onChange={(e) => setNewTag(e.target.value)}
                  placeholder="Add tag..."
                  className="flex-1 border-2 border-gray-200 focus:border-italian-green rounded-xl bg-white shadow-sm"
                  onKeyPress={(e) => e.key === 'Enter' && addTag()}
                />
                <Button 
                  onClick={addTag}
                  className="bg-gradient-to-r from-italian-green to-italian-red hover:from-italian-red hover:to-italian-green transition-all duration-300 border-0 rounded-xl"
                >
                  <Plus className="h-4 w-4" />
                </Button>
              </div>
              
              <div className="flex flex-wrap gap-2">
                {tags.map((tag) => (
                  <Badge 
                    key={tag} 
                    variant="secondary" 
                    className="bg-gradient-to-r from-italian-green to-italian-red text-white border-0 px-3 py-1 flex items-center gap-2"
                  >
                    {tag}
                    <button 
                      onClick={() => removeTag(tag)}
                      className="hover:bg-white/20 rounded-full p-0.5 transition-colors duration-200"
                    >
                      <X className="h-3 w-3" />
                    </button>
                  </Badge>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Status */}
          <Card className="bg-gradient-to-br from-white to-gray-50 border-0 shadow-xl hover:shadow-2xl transition-all duration-500">
            <CardHeader className="pb-4">
              <CardTitle className="text-xl font-bold text-gray-900 italian-flag-corner">
                Status
              </CardTitle>
            </CardHeader>
            <CardContent>
              <select
                value={status}
                onChange={(e) => setStatus(e.target.value)}
                className="w-full border-2 border-gray-200 focus:border-italian-green rounded-xl px-4 py-3 bg-white shadow-sm font-medium text-gray-700"
              >
                <option value="draft">Draft</option>
                <option value="published">Published</option>
              </select>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
