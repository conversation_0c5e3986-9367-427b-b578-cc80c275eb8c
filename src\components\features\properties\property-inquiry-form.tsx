'use client';

import { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Phone, Mail, MessageSquare } from 'lucide-react';

interface PropertyInquiryFormProps {
  propertyId: string;
}

export function PropertyInquiryForm({
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  propertyId
}: PropertyInquiryFormProps) {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    message: '',
    inquiryType: 'information',
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // TODO: Implement form submission
    console.log('Form submitted:', formData);
  };

  return (
    <div className="space-y-6">
      {/* Quick Contact */}
      <Card>
        <CardHeader>
          <CardTitle>Contact Agent</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <Button className="w-full" size="lg">
            <Phone className="h-4 w-4 mr-2" />
            Call Now
          </Button>
          <Button variant="outline" className="w-full" size="lg">
            <MessageSquare className="h-4 w-4 mr-2" />
            WhatsApp
          </Button>
          <Button variant="outline" className="w-full" size="lg">
            <Mail className="h-4 w-4 mr-2" />
            Email
          </Button>
        </CardContent>
      </Card>

      {/* Inquiry Form */}
      <Card>
        <CardHeader>
          <CardTitle>Send Inquiry</CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <Input
                placeholder="Your Name"
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                required
              />
            </div>
            
            <div>
              <Input
                type="email"
                placeholder="Your Email"
                value={formData.email}
                onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                required
              />
            </div>
            
            <div>
              <Input
                type="tel"
                placeholder="Your Phone"
                value={formData.phone}
                onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
              />
            </div>

            <div>
              <select
                className="w-full border rounded-md px-3 py-2"
                value={formData.inquiryType}
                onChange={(e) => setFormData({ ...formData, inquiryType: e.target.value })}
              >
                <option value="information">Request Information</option>
                <option value="purchase">Interested in Buying</option>
                <option value="rent">Interested in Renting</option>
              </select>
            </div>
            
            <div>
              <textarea
                className="w-full border rounded-md px-3 py-2 min-h-[100px]"
                placeholder="Your Message"
                value={formData.message}
                onChange={(e) => setFormData({ ...formData, message: e.target.value })}
                required
              />
            </div>
            
            <Button type="submit" className="w-full">
              Send Inquiry
            </Button>
          </form>
        </CardContent>
      </Card>

      {/* Schedule Viewing */}
      <Card>
        <CardHeader>
          <CardTitle>Schedule Viewing</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-gray-600 mb-4">
            Book a private viewing at your convenience
          </p>
          <Button variant="outline" className="w-full">
            Schedule Viewing
          </Button>
        </CardContent>
      </Card>
    </div>
  );
}
