# Darden Property & Management - Comprehensive Development Plan

## Project Overview
A modern, full-stack real estate and property management platform for the Moroccan market, featuring a public website, admin panel, and advanced property management capabilities.

### Key Features
- **Public Website**: Home, Properties, Services, About, Career, Contact, Blog/News
- **Property Management**: Listings with carousel views, maps, 3D views, videos
- **Admin Panel**: Property management, user management, contact/request handling
- **Multi-role Authentication**: Admin, Sales Person, User access levels
- **Morocco-specific Content**: FIFA World Cup 2030, CAF 2025, tourism trends
- **Communication**: Contact forms, property inquiries, chat functionality

### Technology Stack
- **Frontend**: Next.js 15, React 19, TypeScript, Tailwind CSS
- **Backend**: Supabase (PostgreSQL, Auth, Storage, Real-time)
- **Deployment**: Vercel (Frontend), Supabase (Backend)
- **Additional**: Maps integration, 3D viewers, video players

## Phase 1: Project Architecture & Setup

### 1.1 Dependencies & Environment Setup
```bash
# Core dependencies
npm install @supabase/supabase-js @supabase/auth-helpers-nextjs
npm install @headlessui/react @heroicons/react
npm install framer-motion clsx tailwind-merge
npm install react-hook-form @hookform/resolvers zod
npm install date-fns lucide-react

# Development dependencies
npm install -D @types/node @tailwindcss/forms @tailwindcss/typography
npm install -D prettier prettier-plugin-tailwindcss
npm install -D @testing-library/react @testing-library/jest-dom vitest
```

### 1.2 Project Structure
```
src/
├── app/                    # Next.js App Router
│   ├── (auth)/            # Auth routes
│   ├── (public)/          # Public routes
│   ├── admin/             # Admin panel
│   └── api/               # API routes
├── components/            # Reusable components
│   ├── ui/               # Base UI components
│   ├── forms/            # Form components
│   ├── layout/           # Layout components
│   └── features/         # Feature-specific components
├── lib/                  # Utilities and configurations
│   ├── supabase/         # Supabase client and types
│   ├── utils/            # Helper functions
│   └── validations/      # Zod schemas
├── hooks/                # Custom React hooks
├── types/                # TypeScript type definitions
└── constants/            # Application constants
```

### 1.3 Configuration Files
- Environment variables setup
- Tailwind CSS configuration
- TypeScript configuration
- ESLint and Prettier setup
- Supabase configuration

## Phase 2: Database Design & Supabase Integration

### 2.1 Database Schema

#### Users Table
```sql
users (
  id: uuid (primary key)
  email: text (unique)
  full_name: text
  role: enum ('admin', 'sales_person', 'user')
  avatar_url: text
  phone: text
  created_at: timestamp
  updated_at: timestamp
)
```

#### Properties Table
```sql
properties (
  id: uuid (primary key)
  title: text
  description: text
  price: decimal
  currency: text
  property_type: enum ('apartment', 'house', 'villa', 'commercial', 'land')
  status: enum ('available', 'sold', 'rented', 'pending')
  bedrooms: integer
  bathrooms: integer
  area: decimal
  location: jsonb
  address: text
  city: text
  region: text
  country: text
  features: text[]
  images: text[]
  videos: text[]
  virtual_tour_url: text
  created_by: uuid (foreign key)
  created_at: timestamp
  updated_at: timestamp
)
```

#### Property Inquiries Table
```sql
property_inquiries (
  id: uuid (primary key)
  property_id: uuid (foreign key)
  user_id: uuid (foreign key, nullable)
  name: text
  email: text
  phone: text
  message: text
  inquiry_type: enum ('purchase', 'rent', 'information')
  status: enum ('new', 'contacted', 'scheduled', 'closed')
  created_at: timestamp
  updated_at: timestamp
)
```

#### Blog Posts Table
```sql
blog_posts (
  id: uuid (primary key)
  title: text
  slug: text (unique)
  content: text
  excerpt: text
  featured_image: text
  category: text
  tags: text[]
  published: boolean
  author_id: uuid (foreign key)
  created_at: timestamp
  updated_at: timestamp
)
```

### 2.2 Row Level Security (RLS) Policies
- Public read access for published properties and blog posts
- User-specific access for inquiries and profile data
- Admin/Sales person access for property management
- Secure file upload policies

### 2.3 Supabase Configuration
- Authentication providers setup
- Storage buckets for images/videos
- Real-time subscriptions for chat
- Edge functions for complex operations

## Phase 3: Core Frontend Infrastructure

### 3.1 Layout System
- Root layout with navigation
- Public layout for main website
- Admin layout for dashboard
- Responsive design patterns

### 3.2 Component Library
- Button, Input, Modal, Card components
- Form components with validation
- Loading states and error boundaries
- Accessibility compliance

### 3.3 Routing Structure
```
/                          # Home page
/properties               # Property listings
/properties/[id]          # Property details
/services                 # Services page
/about                    # About page
/career                   # Career page
/contact                  # Contact page
/blog                     # Blog listing
/blog/[slug]              # Blog post
/auth/login               # Login page
/auth/register            # Registration
/admin                    # Admin dashboard
/admin/properties         # Property management
/admin/users              # User management
/admin/inquiries          # Inquiry management
```

## Phase 4: Authentication & User Management

### 4.1 Authentication Flow
- Email/password authentication
- Social login options (Google, Facebook)
- Role-based access control
- Password reset functionality

### 4.2 User Roles & Permissions
- **Admin**: Full system access
- **Sales Person**: Property and inquiry management
- **User**: Property browsing and inquiries

### 4.3 Protected Routes
- Middleware for route protection
- Role-based component rendering
- Session management

## Phase 5: Public Website Development

### 5.1 Home Page
- Hero section with search functionality
- Featured properties carousel
- Morocco FIFA 2030 & CAF 2025 highlights
- Tourism trends and statistics
- Company overview and testimonials

### 5.2 Properties Page
- Advanced search and filtering
- Grid/list view toggle
- Pagination and infinite scroll
- Map integration
- Saved properties functionality

### 5.3 Property Details Page
- Image/video carousel
- 3D virtual tour integration
- Property specifications
- Location map
- Inquiry form
- Similar properties suggestions

### 5.4 Additional Pages
- Services: Property management and real estate services
- About: Company history and team
- Career: Job listings and application forms
- Contact: Contact information and form
- Blog: News, trends, and market insights

## Phase 6: Property Management System

### 6.1 Property Listing Features
- Multiple image upload with drag-and-drop
- Video upload and embedding
- 3D tour URL integration
- Location picker with map
- Rich text editor for descriptions

### 6.2 Search & Filter System
- Price range filtering
- Property type selection
- Location-based search
- Feature-based filtering
- Advanced search options

### 6.3 Media Management
- Image optimization and compression
- Video streaming optimization
- 3D tour integration
- Virtual staging capabilities

## Phase 7: Admin Panel Development

### 7.1 Dashboard Overview
- Key metrics and analytics
- Recent activities
- Quick actions
- Performance indicators

### 7.2 Property Management
- CRUD operations for properties
- Bulk operations
- Status management
- Media management
- SEO optimization

### 7.3 User Management
- User roles and permissions
- Account activation/deactivation
- Activity monitoring
- Communication tools

### 7.4 Inquiry Management
- Inquiry tracking and status updates
- Communication history
- Assignment to sales persons
- Follow-up reminders

## Phase 8: Communication & Request Management

### 8.1 Contact Forms
- General contact form
- Property-specific inquiry forms
- Career application forms
- Newsletter subscription

### 8.2 Chat System
- Real-time messaging
- File sharing capabilities
- Chat history
- Notification system

### 8.3 Email Integration
- Automated email responses
- Inquiry notifications
- Newsletter system
- Marketing campaigns

## Phase 9: Advanced Features & Integrations

### 9.1 Map Integration
- Google Maps or Mapbox integration
- Property location markers
- Area information
- Nearby amenities

### 9.2 3D & Virtual Tours
- 3D model viewer integration
- Virtual tour embedding
- 360-degree photo support
- Interactive floor plans

### 9.3 Morocco-Specific Content
- FIFA World Cup 2030 content
- CAF 2025 information
- Tourism statistics
- Market trends and insights

## Phase 10: Testing, Optimization & Deployment

### 10.1 Testing Strategy
- Unit tests for utilities and hooks
- Integration tests for API routes
- E2E tests for critical user flows
- Performance testing

### 10.2 Performance Optimization
- Image optimization and lazy loading
- Code splitting and bundling
- Caching strategies
- SEO optimization

### 10.3 Deployment & DevOps
- Vercel deployment configuration
- Environment management
- CI/CD pipeline setup
- Monitoring and analytics

## Development Timeline

**Phase 1-2**: 1-2 weeks (Setup & Database)
**Phase 3-4**: 2-3 weeks (Infrastructure & Auth)
**Phase 5**: 3-4 weeks (Public Website)
**Phase 6-7**: 4-5 weeks (Property & Admin Systems)
**Phase 8-9**: 2-3 weeks (Communication & Advanced Features)
**Phase 10**: 1-2 weeks (Testing & Deployment)

**Total Estimated Timeline**: 13-19 weeks

## Next Steps

1. Begin with Phase 1: Project Architecture & Setup
2. Set up development environment and dependencies
3. Configure Supabase project and database schema
4. Implement core infrastructure and authentication
5. Develop public website pages progressively
6. Build admin panel and management features
7. Add advanced features and integrations
8. Test, optimize, and deploy

This plan provides a comprehensive roadmap for building a professional, scalable real estate platform tailored for the Moroccan market.
