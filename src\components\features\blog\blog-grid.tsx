'use client';

import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Calendar, User, ArrowRight, Eye } from 'lucide-react';
import Link from 'next/link';

// Mock blog posts - will be replaced with real data from Supabase
const blogPosts = [
  {
    id: '1',
    title: 'Morocco Real Estate Market Trends 2024',
    slug: 'morocco-real-estate-trends-2024',
    excerpt: 'Discover the latest trends shaping Morocco\'s real estate market in 2024, including price movements and investment opportunities.',
    category: 'Market Analysis',
    author: '<PERSON>',
    publishedAt: '2024-01-15',
    readTime: '5 min read',
    image: '/blog/market-trends.jpg',
  },
  {
    id: '2',
    title: 'FIFA World Cup 2030: Impact on Moroccan Property Market',
    slug: 'fifa-2030-morocco-property-impact',
    excerpt: 'How the FIFA World Cup 2030 is transforming Morocco\'s property landscape and creating new investment opportunities.',
    category: 'Investment',
    author: '<PERSON><PERSON>',
    publishedAt: '2024-01-10',
    readTime: '7 min read',
    image: '/blog/fifa-2030.jpg',
  },
  {
    id: '3',
    title: 'CAF 2025: Boosting Tourism and Real Estate',
    slug: 'caf-2025-tourism-real-estate-boost',
    excerpt: 'The upcoming CAF Africa Cup of Nations 2025 in Morocco is set to boost tourism and create new opportunities in the real estate sector.',
    category: 'Tourism',
    author: 'Youssef Mansouri',
    publishedAt: '2024-01-08',
    readTime: '4 min read',
    image: '/blog/caf-2025.jpg',
  },
  {
    id: '4',
    title: 'Best Neighborhoods to Invest in Casablanca',
    slug: 'best-neighborhoods-casablanca-investment',
    excerpt: 'A comprehensive guide to the most promising neighborhoods in Casablanca for real estate investment.',
    category: 'Investment Guide',
    author: 'Sarah El Fassi',
    publishedAt: '2024-01-05',
    readTime: '6 min read',
    image: '/blog/casablanca-neighborhoods.jpg',
  },
  {
    id: '5',
    title: 'Understanding Moroccan Property Laws for Foreigners',
    slug: 'moroccan-property-laws-foreigners',
    excerpt: 'Everything foreign investors need to know about buying property in Morocco, including legal requirements and procedures.',
    category: 'Legal Guide',
    author: 'Ahmed Benali',
    publishedAt: '2024-01-03',
    readTime: '8 min read',
    image: '/blog/property-laws.jpg',
  },
  {
    id: '6',
    title: 'Luxury Villa Market in Marrakech: 2024 Outlook',
    slug: 'luxury-villa-market-marrakech-2024',
    excerpt: 'An in-depth analysis of the luxury villa market in Marrakech and what to expect in 2024.',
    category: 'Luxury Market',
    author: 'Fatima Alaoui',
    publishedAt: '2024-01-01',
    readTime: '5 min read',
    image: '/blog/marrakech-villas.jpg',
  },
];

export function BlogGrid() {
  return (
    <div className="space-y-10">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 bg-gradient-to-r from-white to-gray-50 p-6 rounded-2xl shadow-lg border-0">
        <p className="text-lg font-semibold text-gray-700">
          Showing {blogPosts.length} articles
        </p>
        <select className="border-2 border-gray-200 focus:border-italian-green rounded-xl px-4 py-3 bg-white shadow-sm font-medium text-gray-700 min-w-[200px]">
          <option>Sort by: Latest</option>
          <option>Most Popular</option>
          <option>Oldest First</option>
        </select>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-10">
        {blogPosts.map((post, index) => (
          <Card key={post.id} className="overflow-hidden hover:shadow-2xl transition-all duration-500 hover-lift group bg-gradient-to-br from-white to-gray-50 border-0 shadow-lg animate-scale-in" style={{ animationDelay: `${index * 100}ms` }}>
            <div className="aspect-[16/10] bg-gradient-to-br from-gray-200 to-gray-300 relative overflow-hidden">
              <div className="absolute inset-0 bg-gradient-to-br from-italian-green/10 to-italian-red/10"></div>
              <div className="absolute inset-0 flex items-center justify-center text-gray-400 group-hover:text-italian-green transition-colors duration-300">
                <Eye className="h-16 w-16 group-hover:scale-110 transition-transform duration-300" />
              </div>
              <div className="absolute top-4 left-4">
                <Badge variant="secondary" className="bg-gradient-to-r from-italian-green to-italian-red text-white border-0 px-3 py-1 font-semibold">
                  {post.category}
                </Badge>
              </div>
              <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            </div>

            <CardContent className="p-8">
              <div className="mb-6">
                <h3 className="text-2xl font-bold text-gray-900 mb-4 line-clamp-2 group-hover:text-italian-green transition-colors duration-300">
                  {post.title}
                </h3>
                <p className="text-gray-600 leading-relaxed line-clamp-3">
                  {post.excerpt}
                </p>
              </div>

              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-6">
                <div className="flex items-center space-x-6 text-sm text-gray-600">
                  <div className="flex items-center bg-gray-100 px-3 py-1 rounded-full">
                    <User className="h-4 w-4 mr-2 text-italian-green" />
                    <span className="font-medium">{post.author}</span>
                  </div>
                  <div className="flex items-center bg-gray-100 px-3 py-1 rounded-full">
                    <Calendar className="h-4 w-4 mr-2 text-italian-green" />
                    <span className="font-medium">{new Date(post.publishedAt).toLocaleDateString()}</span>
                  </div>
                </div>
                <div className="bg-gradient-to-r from-italian-green to-italian-red bg-clip-text text-transparent font-bold">
                  {post.readTime}
                </div>
              </div>

              <Button asChild className="w-full bg-gradient-to-r from-italian-green to-italian-red hover:from-italian-red hover:to-italian-green transition-all duration-300 border-0 py-3 text-lg font-semibold shadow-lg hover:shadow-xl group">
                <Link href={`/blog/${post.slug}`}>
                  Read More
                  <ArrowRight className="ml-3 h-5 w-5 group-hover:translate-x-1 transition-transform duration-300" />
                </Link>
              </Button>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Pagination */}
      <div className="flex justify-center mt-16">
        <div className="flex items-center space-x-3 bg-gradient-to-r from-white to-gray-50 p-6 rounded-2xl shadow-lg border-0">
          <Button variant="outline" disabled className="border-2 border-gray-200 rounded-xl px-6 py-3">
            Previous
          </Button>
          <Button variant="outline" className="border-2 border-gray-200 hover:border-italian-green hover:bg-italian-green hover:text-white transition-all duration-300 rounded-xl px-4 py-3">
            1
          </Button>
          <Button className="bg-gradient-to-r from-italian-green to-italian-red border-0 rounded-xl px-4 py-3 shadow-lg">
            2
          </Button>
          <Button variant="outline" className="border-2 border-gray-200 hover:border-italian-green hover:bg-italian-green hover:text-white transition-all duration-300 rounded-xl px-4 py-3">
            3
          </Button>
          <Button variant="outline" className="border-2 border-gray-200 hover:border-italian-green hover:bg-italian-green hover:text-white transition-all duration-300 rounded-xl px-6 py-3">
            Next
          </Button>
        </div>
      </div>
    </div>
  );
}
