import Link from 'next/link';
import { Mail } from 'lucide-react';

export default function VerifyEmailPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-100 flex flex-col justify-center py-12 sm:px-6 lg:px-8 relative overflow-hidden">
      {/* Background decorations */}
      <div className="absolute top-0 left-0 w-full h-2 bg-gradient-to-r from-italian-green via-white to-italian-red"></div>
      <div className="absolute top-10 right-10 w-32 h-32 bg-gradient-to-br from-italian-green/10 to-italian-red/10 rounded-full blur-xl"></div>
      <div className="absolute bottom-10 left-10 w-40 h-40 bg-gradient-to-br from-italian-red/10 to-italian-green/10 rounded-full blur-xl"></div>

      <div className="sm:mx-auto sm:w-full sm:max-w-md relative z-10">
        <Link href="/" className="flex items-center justify-center space-x-3 animate-fade-in">
          <div className="w-14 h-14 bg-gradient-to-br from-italian-green to-italian-red rounded-2xl flex items-center justify-center shadow-xl">
            <span className="text-white font-bold text-2xl">D</span>
          </div>
          <span className="font-bold text-3xl text-gray-900 bg-gradient-to-r from-italian-green to-italian-red bg-clip-text text-transparent">
            Darden Property & Management
          </span>
        </Link>
      </div>

      <div className="mt-12 sm:mx-auto sm:w-full sm:max-w-md relative z-10">
        <div className="bg-gradient-to-br from-white to-gray-50 py-12 px-8 shadow-2xl rounded-3xl border-0 animate-scale-in">
          <div className="space-y-8">
            <div className="text-center">
              <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-gradient-to-br from-italian-green to-italian-red shadow-xl">
                <Mail className="h-8 w-8 text-white" />
              </div>
              <h2 className="mt-6 text-center text-4xl font-bold text-gray-900 italian-flag-corner">
                Check Your Email
              </h2>
              <p className="mt-4 text-center text-lg text-gray-600">
                We&apos;ve sent a verification link to your email address.
              </p>
            </div>

            <div className="rounded-2xl bg-gradient-to-r from-blue-50 to-blue-100 p-6 border-l-4 border-italian-green">
              <div className="text-blue-800">
                <p className="font-bold text-lg mb-4">Next steps:</p>
                <ol className="space-y-3 text-sm">
                  <li className="flex items-center">
                    <div className="w-2 h-2 bg-italian-green rounded-full mr-3"></div>
                    Check your email inbox (and spam folder)
                  </li>
                  <li className="flex items-center">
                    <div className="w-2 h-2 bg-italian-green rounded-full mr-3"></div>
                    Click the verification link in the email
                  </li>
                  <li className="flex items-center">
                    <div className="w-2 h-2 bg-italian-green rounded-full mr-3"></div>
                    Return to sign in to your account
                  </li>
                </ol>
              </div>
            </div>

            <div className="text-center space-y-4">
              <p className="text-gray-600">
                Didn&apos;t receive the email?{' '}
                <button className="font-bold text-italian-green hover:text-italian-red transition-colors duration-300">
                  Resend verification
                </button>
              </p>
              <p className="text-gray-600">
                <Link
                  href="/auth/login"
                  className="font-bold text-italian-green hover:text-italian-red transition-colors duration-300"
                >
                  Back to sign in
                </Link>
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
