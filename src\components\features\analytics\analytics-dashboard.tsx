import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { 
  BarChart3, 
  TrendingUp, 
  Eye, 
  Users, 
  MapPin,
  Calendar,
  DollarSign,
  Activity
} from 'lucide-react';

const analyticsData = {
  overview: [
    {
      title: 'Total Views',
      value: '12,345',
      change: '+15.3%',
      trend: 'up',
      icon: Eye,
    },
    {
      title: 'Unique Visitors',
      value: '8,901',
      change: '+12.1%',
      trend: 'up',
      icon: Users,
    },
    {
      title: 'Property Inquiries',
      value: '234',
      change: '+8.7%',
      trend: 'up',
      icon: Activity,
    },
    {
      title: 'Avg. Property Value',
      value: '1.2M MAD',
      change: '+5.2%',
      trend: 'up',
      icon: DollarSign,
    },
  ],
  topCities: [
    { city: 'Casablanca', views: 4521, inquiries: 89 },
    { city: 'Rabat', views: 3210, inquiries: 67 },
    { city: 'Marrakech', views: 2890, inquiries: 54 },
    { city: 'Fes', views: 1567, inquiries: 32 },
    { city: 'Tangier', views: 1234, inquiries: 28 },
  ],
  propertyTypes: [
    { type: 'Apartments', count: 156, percentage: 45 },
    { type: 'Villas', count: 89, percentage: 26 },
    { type: 'Houses', count: 67, percentage: 19 },
    { type: 'Commercial', count: 23, percentage: 7 },
    { type: 'Land', count: 12, percentage: 3 },
  ],
  monthlyTrends: [
    { month: 'Jan', views: 8500, inquiries: 180 },
    { month: 'Feb', views: 9200, inquiries: 195 },
    { month: 'Mar', views: 10100, inquiries: 210 },
    { month: 'Apr', views: 11300, inquiries: 225 },
    { month: 'May', views: 12100, inquiries: 240 },
    { month: 'Jun', views: 12800, inquiries: 255 },
  ],
};

export function AnalyticsDashboard() {
  return (
    <div className="space-y-8">
      <div>
        <h1 className="text-3xl font-bold text-gray-900">Analytics Dashboard</h1>
        <p className="text-gray-600">Track performance and gain insights into your property listings.</p>
      </div>

      {/* Overview Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {analyticsData.overview.map((stat, index) => {
          const Icon = stat.icon;
          return (
            <Card key={index}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-gray-600">
                  {stat.title}
                </CardTitle>
                <Icon className="h-4 w-4 text-gray-400" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stat.value}</div>
                <div className="flex items-center text-xs text-green-600 mt-1">
                  <TrendingUp className="h-3 w-3 mr-1" />
                  {stat.change} from last month
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Top Cities */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <MapPin className="mr-2 h-5 w-5" />
              Top Cities by Views
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {analyticsData.topCities.map((city, index) => (
                <div key={city.city} className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center text-primary font-medium text-sm">
                      {index + 1}
                    </div>
                    <div>
                      <div className="font-medium text-gray-900">{city.city}</div>
                      <div className="text-sm text-gray-500">{city.inquiries} inquiries</div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="font-medium text-gray-900">{city.views.toLocaleString()}</div>
                    <div className="text-sm text-gray-500">views</div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Property Types */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <BarChart3 className="mr-2 h-5 w-5" />
              Property Types Distribution
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {analyticsData.propertyTypes.map((type) => (
                <div key={type.type} className="space-y-2">
                  <div className="flex items-center justify-between text-sm">
                    <span className="font-medium text-gray-900">{type.type}</span>
                    <span className="text-gray-500">{type.count} properties</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-primary h-2 rounded-full transition-all duration-300"
                      style={{ width: `${type.percentage}%` }}
                    ></div>
                  </div>
                  <div className="text-xs text-gray-500">{type.percentage}%</div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Monthly Trends */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Calendar className="mr-2 h-5 w-5" />
            Monthly Trends
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="grid grid-cols-6 gap-4">
              {analyticsData.monthlyTrends.map((month) => (
                <div key={month.month} className="text-center">
                  <div className="text-sm font-medium text-gray-900 mb-2">
                    {month.month}
                  </div>
                  <div className="space-y-2">
                    <div className="bg-blue-100 rounded-lg p-3">
                      <div className="text-lg font-bold text-blue-600">
                        {(month.views / 1000).toFixed(1)}k
                      </div>
                      <div className="text-xs text-blue-600">Views</div>
                    </div>
                    <div className="bg-green-100 rounded-lg p-3">
                      <div className="text-lg font-bold text-green-600">
                        {month.inquiries}
                      </div>
                      <div className="text-xs text-green-600">Inquiries</div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Performance Insights */}
      <Card>
        <CardHeader>
          <CardTitle>Performance Insights</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <div className="text-2xl font-bold text-blue-600 mb-2">15.3%</div>
              <div className="text-sm text-blue-600 font-medium">Increase in Views</div>
              <div className="text-xs text-gray-600 mt-1">Compared to last month</div>
            </div>
            <div className="text-center p-4 bg-green-50 rounded-lg">
              <div className="text-2xl font-bold text-green-600 mb-2">8.7%</div>
              <div className="text-sm text-green-600 font-medium">More Inquiries</div>
              <div className="text-xs text-gray-600 mt-1">Higher conversion rate</div>
            </div>
            <div className="text-center p-4 bg-purple-50 rounded-lg">
              <div className="text-2xl font-bold text-purple-600 mb-2">2.3x</div>
              <div className="text-sm text-purple-600 font-medium">Better Engagement</div>
              <div className="text-xs text-gray-600 mt-1">Time spent on listings</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
