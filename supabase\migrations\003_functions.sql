-- Function to handle new user registration
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.users (id, email, full_name)
    VALUES (
        NEW.id,
        NEW.email,
        COALESCE(NEW.raw_user_meta_data->>'full_name', NEW.email)
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to automatically create user profile
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Function to get user role
CREATE OR REPLACE FUNCTION public.get_user_role(user_id UUID)
RETURNS TEXT AS $$
DECLARE
    user_role TEXT;
BEGIN
    SELECT role INTO user_role
    FROM public.users
    WHERE id = user_id;
    
    RETURN COALESCE(user_role, 'user');
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check if user is admin
CREATE OR <PERSON><PERSON><PERSON>CE FUNCTION public.is_admin(user_id UUID)
<PERSON><PERSON><PERSON>NS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM public.users
        WHERE id = user_id AND role = 'admin'
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check if user is admin or sales person
CREATE OR REPLACE FUNCTION public.is_admin_or_sales(user_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM public.users
        WHERE id = user_id AND role IN ('admin', 'sales_person')
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to search properties
CREATE OR REPLACE FUNCTION public.search_properties(
    search_query TEXT DEFAULT NULL,
    property_type_filter property_type DEFAULT NULL,
    min_price_filter DECIMAL DEFAULT NULL,
    max_price_filter DECIMAL DEFAULT NULL,
    city_filter TEXT DEFAULT NULL,
    region_filter TEXT DEFAULT NULL,
    bedrooms_filter INTEGER DEFAULT NULL,
    bathrooms_filter INTEGER DEFAULT NULL,
    features_filter TEXT[] DEFAULT NULL
)
RETURNS TABLE (
    id UUID,
    title TEXT,
    description TEXT,
    price DECIMAL,
    currency TEXT,
    property_type property_type,
    status property_status,
    bedrooms INTEGER,
    bathrooms INTEGER,
    area DECIMAL,
    address TEXT,
    city TEXT,
    region TEXT,
    country TEXT,
    features TEXT[],
    images TEXT[],
    virtual_tour_url TEXT,
    created_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        p.id, p.title, p.description, p.price, p.currency,
        p.property_type, p.status, p.bedrooms, p.bathrooms,
        p.area, p.address, p.city, p.region, p.country,
        p.features, p.images, p.virtual_tour_url, p.created_at
    FROM public.properties p
    WHERE 
        p.status = 'available'
        AND (search_query IS NULL OR (
            p.title ILIKE '%' || search_query || '%' OR
            p.description ILIKE '%' || search_query || '%' OR
            p.address ILIKE '%' || search_query || '%'
        ))
        AND (property_type_filter IS NULL OR p.property_type = property_type_filter)
        AND (min_price_filter IS NULL OR p.price >= min_price_filter)
        AND (max_price_filter IS NULL OR p.price <= max_price_filter)
        AND (city_filter IS NULL OR p.city ILIKE '%' || city_filter || '%')
        AND (region_filter IS NULL OR p.region ILIKE '%' || region_filter || '%')
        AND (bedrooms_filter IS NULL OR p.bedrooms >= bedrooms_filter)
        AND (bathrooms_filter IS NULL OR p.bathrooms >= bathrooms_filter)
        AND (features_filter IS NULL OR p.features && features_filter)
    ORDER BY p.created_at DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
