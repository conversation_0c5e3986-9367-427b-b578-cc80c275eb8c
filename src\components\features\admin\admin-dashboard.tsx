import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Users,
  Building,
  MessageSquare,
  FileText,
  TrendingUp,
  Activity,
  Eye
} from 'lucide-react';

const stats = [
  {
    title: 'Total Users',
    value: '1,234',
    change: '+12% from last month',
    icon: Users,
    color: 'text-blue-600',
  },
  {
    title: 'Total Properties',
    value: '456',
    change: '+8% from last month',
    icon: Building,
    color: 'text-green-600',
  },
  {
    title: 'Active Inquiries',
    value: '89',
    change: '+23% from last month',
    icon: MessageSquare,
    color: 'text-purple-600',
  },
  {
    title: 'Blog Posts',
    value: '67',
    change: '+5% from last month',
    icon: FileText,
    color: 'text-orange-600',
  },
];

const recentActivity = [
  {
    type: 'user_registered',
    message: 'New user Ahmed <PERSON> registered',
    time: '2 minutes ago',
  },
  {
    type: 'property_added',
    message: 'New property added in Casablanca',
    time: '15 minutes ago',
  },
  {
    type: 'inquiry_received',
    message: 'New inquiry for Villa in Marrakech',
    time: '1 hour ago',
  },
  {
    type: 'blog_published',
    message: 'Blog post "Market Trends 2024" published',
    time: '2 hours ago',
  },
];

const topProperties = [
  {
    title: 'Luxury Villa in Casablanca',
    views: 234,
    inquiries: 12,
    price: '2,500,000 MAD',
  },
  {
    title: 'Modern Apartment in Rabat',
    views: 189,
    inquiries: 8,
    price: '850,000 MAD',
  },
  {
    title: 'Traditional Riad in Marrakech',
    views: 156,
    inquiries: 15,
    price: '1,200,000 MAD',
  },
];

export function AdminDashboard() {
  return (
    <div className="space-y-8">
      <div>
        <h1 className="text-3xl font-bold text-gray-900 italian-flag-corner">Admin Dashboard</h1>
        <p className="text-gray-600">Overview of system performance and key metrics.</p>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat, index) => {
          const Icon = stat.icon;
          return (
            <Card key={index} className="hover:shadow-lg transition-all duration-300 hover-lift fade-in">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-gray-600">
                  {stat.title}
                </CardTitle>
                <Icon className={`h-4 w-4 ${stat.color}`} />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stat.value}</div>
                <p className="text-xs text-gray-600 mt-1">{stat.change}</p>
              </CardContent>
            </Card>
          );
        })}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Recent Activity */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Activity className="mr-2 h-5 w-5" />
              Recent Activity
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {recentActivity.map((activity, index) => (
                <div key={index} className="flex items-start space-x-3">
                  <div className="w-2 h-2 bg-primary rounded-full mt-2"></div>
                  <div className="flex-1">
                    <p className="text-sm text-gray-900">{activity.message}</p>
                    <p className="text-xs text-gray-500">{activity.time}</p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Top Properties */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <TrendingUp className="mr-2 h-5 w-5" />
              Top Performing Properties
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {topProperties.map((property, index) => (
                <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                  <div>
                    <h4 className="font-medium text-gray-900 text-sm">
                      {property.title}
                    </h4>
                    <div className="flex items-center space-x-4 text-xs text-gray-500 mt-1">
                      <div className="flex items-center">
                        <Eye className="h-3 w-3 mr-1" />
                        {property.views} views
                      </div>
                      <div className="flex items-center">
                        <MessageSquare className="h-3 w-3 mr-1" />
                        {property.inquiries} inquiries
                      </div>
                    </div>
                  </div>
                  <div className="text-sm font-medium text-primary">
                    {property.price}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <button className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-all duration-300 hover-lift scale-in">
              <Users className="h-8 w-8 text-blue-600 mx-auto mb-2" />
              <div className="text-sm font-medium">Manage Users</div>
            </button>
            <button className="p-4 border border-gray-200 rounded-lg hover:bg-italian-green hover:text-white transition-all duration-300 hover-lift scale-in">
              <Building className="h-8 w-8 text-green-600 mx-auto mb-2" />
              <div className="text-sm font-medium">Add Property</div>
            </button>
            <button className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-all duration-300 hover-lift scale-in">
              <FileText className="h-8 w-8 text-purple-600 mx-auto mb-2" />
              <div className="text-sm font-medium">Create Blog Post</div>
            </button>
            <button className="p-4 border border-gray-200 rounded-lg hover:bg-italian-red hover:text-white transition-all duration-300 hover-lift scale-in">
              <MessageSquare className="h-8 w-8 text-orange-600 mx-auto mb-2" />
              <div className="text-sm font-medium">View Inquiries</div>
            </button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
