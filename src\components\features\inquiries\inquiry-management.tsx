'use client';

import { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import {
  Search,
  Filter,
  MoreHorizontal,
  MessageSquare,
  Phone,
  Mail,
  Building,
  User,
  Clock
} from 'lucide-react';

// Mock data - will be replaced with real data from Supabase
const inquiries = [
  {
    id: '1',
    name: '<PERSON>',
    email: '<EMAIL>',
    phone: '+212 6XX XXX XXX',
    property_title: 'Luxury Villa in Casablanca',
    inquiry_type: 'purchase',
    status: 'new',
    message: 'I am interested in viewing this beautiful villa. Could we schedule a visit?',
    created_at: '2024-01-20T10:30:00Z',
  },
  {
    id: '2',
    name: '<PERSON>',
    email: '<EMAIL>',
    phone: '+212 5XX XXX XXX',
    property_title: 'Modern Apartment in Rabat',
    inquiry_type: 'rent',
    status: 'contacted',
    message: 'Looking for a rental property in Rabat. Is this apartment available for rent?',
    created_at: '2024-01-19T14:15:00Z',
  },
  {
    id: '3',
    name: '<PERSON> <PERSON>ami',
    email: '<EMAIL>',
    phone: '+212 6XX XXX XXX',
    property_title: 'Traditional Riad in Marrakech',
    inquiry_type: 'purchase',
    status: 'scheduled',
    message: 'Interested in purchasing this riad for investment purposes. Please provide more details.',
    created_at: '2024-01-18T09:45:00Z',
  },
  {
    id: '4',
    name: 'Fatima El Fassi',
    email: '<EMAIL>',
    phone: '+212 6XX XXX XXX',
    property_title: 'Beachfront Apartment in Agadir',
    inquiry_type: 'information',
    status: 'closed',
    message: 'Can you provide more information about the amenities and nearby facilities?',
    created_at: '2024-01-17T16:20:00Z',
  },
];

export function InquiryManagement() {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedStatus, setSelectedStatus] = useState('all');
  const [selectedType, setSelectedType] = useState('all');

  const filteredInquiries = inquiries.filter(inquiry => {
    const matchesSearch = inquiry.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         inquiry.property_title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         inquiry.email.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesStatus = selectedStatus === 'all' || inquiry.status === selectedStatus;
    const matchesType = selectedType === 'all' || inquiry.inquiry_type === selectedType;
    return matchesSearch && matchesStatus && matchesType;
  });

  const getStatusColor = (status: string): 'info' | 'warning' | 'secondary' | 'success' => {
    switch (status) {
      case 'new': return 'info';
      case 'contacted': return 'warning';
      case 'scheduled': return 'secondary';
      case 'closed': return 'success';
      default: return 'secondary';
    }
  };

  const getTypeColor = (type: string): 'success' | 'info' | 'secondary' => {
    switch (type) {
      case 'purchase': return 'success';
      case 'rent': return 'info';
      case 'information': return 'secondary';
      default: return 'secondary';
    }
  };

  const getTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) return 'Just now';
    if (diffInHours < 24) return `${diffInHours} hours ago`;
    const diffInDays = Math.floor(diffInHours / 24);
    return `${diffInDays} days ago`;
  };

  return (
    <div className="space-y-6">
      {/* Filters */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Search inquiries..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
            
            <select
              value={selectedStatus}
              onChange={(e) => setSelectedStatus(e.target.value)}
              className="border rounded-md px-3 py-2"
            >
              <option value="all">All Status</option>
              <option value="new">New</option>
              <option value="contacted">Contacted</option>
              <option value="scheduled">Scheduled</option>
              <option value="closed">Closed</option>
            </select>

            <select
              value={selectedType}
              onChange={(e) => setSelectedType(e.target.value)}
              className="border rounded-md px-3 py-2"
            >
              <option value="all">All Types</option>
              <option value="purchase">Purchase</option>
              <option value="rent">Rent</option>
              <option value="information">Information</option>
            </select>
            
            <Button variant="outline">
              <Filter className="mr-2 h-4 w-4" />
              More Filters
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Inquiries List */}
      <div className="space-y-4">
        {filteredInquiries.map((inquiry) => (
          <Card key={inquiry.id} className="hover:shadow-md transition-shadow">
            <CardContent className="p-6">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-3 mb-3">
                    <div className="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center">
                      <User className="h-5 w-5 text-gray-600" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900">{inquiry.name}</h3>
                      <div className="flex items-center space-x-4 text-sm text-gray-600">
                        <div className="flex items-center">
                          <Mail className="h-3 w-3 mr-1" />
                          {inquiry.email}
                        </div>
                        <div className="flex items-center">
                          <Phone className="h-3 w-3 mr-1" />
                          {inquiry.phone}
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="mb-3">
                    <div className="flex items-center space-x-2 mb-2">
                      <Building className="h-4 w-4 text-gray-400" />
                      <span className="font-medium text-gray-900">{inquiry.property_title}</span>
                      <Badge variant={getTypeColor(inquiry.inquiry_type)}>
                        {inquiry.inquiry_type}
                      </Badge>
                      <Badge variant={getStatusColor(inquiry.status)}>
                        {inquiry.status}
                      </Badge>
                    </div>
                  </div>

                  <div className="mb-4">
                    <p className="text-gray-700 text-sm">{inquiry.message}</p>
                  </div>

                  <div className="flex items-center text-sm text-gray-500">
                    <Clock className="h-3 w-3 mr-1" />
                    {getTimeAgo(inquiry.created_at)}
                  </div>
                </div>

                <div className="flex items-center space-x-2 ml-4">
                  <Button size="sm">
                    <MessageSquare className="h-4 w-4 mr-2" />
                    Respond
                  </Button>
                  <Button variant="outline" size="sm">
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Pagination */}
      <div className="flex justify-center">
        <div className="flex items-center space-x-2">
          <Button variant="outline" disabled>Previous</Button>
          <Button variant="outline">1</Button>
          <Button>2</Button>
          <Button variant="outline">3</Button>
          <Button variant="outline">Next</Button>
        </div>
      </div>
    </div>
  );
}
