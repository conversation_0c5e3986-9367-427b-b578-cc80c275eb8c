'use client';

import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Bed, Bath, Square, MapPin, Eye, Heart, Loader2 } from 'lucide-react';
import Link from 'next/link';
import type { Database } from '@/types/database';

type Property = Database['public']['Tables']['properties']['Row'];

interface PropertyGridProps {
  properties: Property[];
  loading: boolean;
  error: string | null;
  totalCount: number;
}

export function PropertyGrid({ properties, loading, error, totalCount }: PropertyGridProps) {
  if (loading) {
    return (
      <div className="space-y-8">
        {/* Modern Loading State */}
        <div className="flex items-center justify-center py-16">
          <div className="text-center">
            <div className="relative">
              <div className="w-16 h-16 bg-gradient-to-r from-emerald-500 to-blue-500 rounded-full animate-pulse mx-auto mb-4"></div>
              <Loader2 className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 h-8 w-8 animate-spin text-white" />
            </div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">Finding Perfect Properties</h3>
            <p className="text-gray-600">Please wait while we search for the best matches...</p>
          </div>
        </div>

        {/* Loading Skeleton */}
        <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-8">
          {[...Array(6)].map((_, i) => (
            <div key={i} className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg overflow-hidden animate-pulse">
              <div className="aspect-[4/3] bg-gradient-to-br from-gray-200 to-gray-300"></div>
              <div className="p-6 space-y-4">
                <div className="h-6 bg-gradient-to-r from-gray-200 to-gray-300 rounded-lg"></div>
                <div className="h-4 bg-gradient-to-r from-gray-200 to-gray-300 rounded-lg w-3/4"></div>
                <div className="grid grid-cols-3 gap-4">
                  <div className="h-16 bg-gradient-to-br from-gray-200 to-gray-300 rounded-xl"></div>
                  <div className="h-16 bg-gradient-to-br from-gray-200 to-gray-300 rounded-xl"></div>
                  <div className="h-16 bg-gradient-to-br from-gray-200 to-gray-300 rounded-xl"></div>
                </div>
                <div className="h-12 bg-gradient-to-r from-gray-200 to-gray-300 rounded-xl"></div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-16">
        <div className="max-w-md mx-auto">
          <div className="w-16 h-16 bg-gradient-to-br from-red-100 to-red-200 rounded-full flex items-center justify-center mx-auto mb-6">
            <Eye className="h-8 w-8 text-red-500" />
          </div>
          <h3 className="text-xl font-semibold text-gray-900 mb-4">Oops! Something went wrong</h3>
          <p className="text-red-600 mb-6">Error loading properties: {error}</p>
          <Button
            onClick={() => window.location.reload()}
            className="bg-gradient-to-r from-red-500 to-pink-500 hover:from-red-600 hover:to-pink-600 text-white border-0 rounded-xl px-6 py-3 font-semibold shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105"
          >
            Try Again
          </Button>
        </div>
      </div>
    );
  }

  if (properties.length === 0) {
    return (
      <div className="text-center py-16">
        <div className="max-w-md mx-auto">
          <div className="w-20 h-20 bg-gradient-to-br from-gray-100 to-gray-200 rounded-full flex items-center justify-center mx-auto mb-6">
            <Eye className="h-10 w-10 text-gray-400" />
          </div>
          <h3 className="text-2xl font-semibold text-gray-900 mb-4">No Properties Found</h3>
          <p className="text-gray-600 mb-2">We couldn't find any properties matching your criteria.</p>
          <p className="text-sm text-gray-500 mb-6">Try adjusting your search filters or browse all properties.</p>
          <Button
            onClick={() => window.location.href = '/properties'}
            className="bg-gradient-to-r from-emerald-500 to-blue-500 hover:from-emerald-600 hover:to-blue-600 text-white border-0 rounded-xl px-6 py-3 font-semibold shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105"
          >
            Browse All Properties
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      <div className="flex items-center justify-between bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-gray-200/50">
        <div className="flex items-center space-x-4">
          <div className="flex items-center justify-center w-12 h-12 bg-gradient-to-br from-emerald-400 to-blue-500 rounded-full">
            <Eye className="h-6 w-6 text-white" />
          </div>
          <div>
            <p className="text-lg font-semibold text-gray-900">
              {totalCount} Properties Found
            </p>
            <p className="text-sm text-gray-600">
              Showing {properties.length} results
            </p>
          </div>
        </div>

        <select className="bg-white/80 backdrop-blur-sm border border-gray-300/50 rounded-xl px-4 py-3 text-gray-700 font-medium shadow-sm hover:shadow-md transition-all duration-300 focus:ring-2 focus:ring-emerald-400/20 focus:border-emerald-400">
          <option>Sort by: Latest</option>
          <option>Price: Low to High</option>
          <option>Price: High to Low</option>
          <option>Area: Largest First</option>
        </select>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-8">
        {properties.map((property, index) => (
          <Card key={property.id} className="group overflow-hidden bg-white/80 backdrop-blur-sm border-0 shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:scale-[1.02] hover:-translate-y-2 animate-fade-in" style={{ animationDelay: `${index * 0.1}s` }}>
            <div className="relative overflow-hidden">
              {/* Modern Image Container with Gradient Overlay */}
              <div className="aspect-[4/3] bg-gradient-to-br from-slate-200 via-gray-100 to-slate-200 relative overflow-hidden">
                {/* Placeholder with modern gradient */}
                <div className="absolute inset-0 bg-gradient-to-br from-emerald-400/20 via-blue-400/20 to-purple-400/20">
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="text-center">
                      <Eye className="h-16 w-16 text-white/60 mx-auto mb-2" />
                      <p className="text-white/80 text-sm font-medium">Property Image</p>
                    </div>
                  </div>
                </div>

                {/* Animated overlay on hover */}
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

                {/* Floating elements */}
                <div className="absolute top-4 left-4 right-4 flex justify-between items-start">
                  <div className="flex flex-col gap-2">
                    <Badge className={`
                      ${property.status === 'available' ? 'bg-gradient-to-r from-emerald-500 to-green-500 text-white border-0' :
                        property.status === 'sold' ? 'bg-gradient-to-r from-red-500 to-pink-500 text-white border-0' :
                        property.status === 'rented' ? 'bg-gradient-to-r from-blue-500 to-indigo-500 text-white border-0' :
                        'bg-gradient-to-r from-yellow-500 to-orange-500 text-white border-0'
                      } shadow-lg backdrop-blur-sm font-medium px-3 py-1
                    `}>
                      {property.status === 'available' ? 'Available' :
                       property.status === 'sold' ? 'Sold' :
                       property.status === 'rented' ? 'Rented' :
                       property.status === 'pending' ? 'Pending' : property.status}
                    </Badge>
                    <Badge variant="secondary" className="bg-white/90 text-gray-700 border-0 shadow-md backdrop-blur-sm font-medium px-3 py-1">
                      {property.property_type}
                    </Badge>
                  </div>

                  <Button size="sm" className="h-10 w-10 p-0 bg-white/20 backdrop-blur-sm border-white/30 hover:bg-gradient-to-r hover:from-pink-500 hover:to-red-500 hover:border-0 transition-all duration-300 transform hover:scale-110 shadow-lg">
                    <Heart className="h-4 w-4 text-white" />
                  </Button>
                </div>

                {/* Price tag - floating */}
                <div className="absolute bottom-4 right-4 bg-gradient-to-r from-emerald-500 to-blue-500 text-white px-4 py-2 rounded-full shadow-lg backdrop-blur-sm font-bold text-lg transform group-hover:scale-105 transition-transform duration-300">
                  {property.price.toLocaleString()} {property.currency}
                </div>
              </div>
            </div>

            <CardContent className="p-6 bg-gradient-to-br from-white to-gray-50/50">
              <div className="mb-6">
                <h3 className="text-xl font-bold text-gray-900 mb-3 line-clamp-2 group-hover:text-emerald-600 transition-colors duration-300">
                  {property.title}
                </h3>
                <div className="flex items-center text-gray-600 mb-4">
                  <div className="flex items-center justify-center w-8 h-8 bg-gradient-to-br from-emerald-100 to-blue-100 rounded-full mr-3">
                    <MapPin className="h-4 w-4 text-emerald-600" />
                  </div>
                  <span className="text-sm line-clamp-1 font-medium">
                    {property.address ? `${property.address}, ` : ''}{property.city}, {property.country}
                  </span>
                </div>
              </div>

              {/* Modern Stats Grid */}
              <div className="grid grid-cols-3 gap-4 mb-6">
                <div className="text-center p-3 bg-gradient-to-br from-emerald-50 to-emerald-100/50 rounded-xl border border-emerald-200/50">
                  <div className="flex items-center justify-center w-8 h-8 bg-gradient-to-br from-emerald-400 to-emerald-500 rounded-full mx-auto mb-2">
                    <Bed className="h-4 w-4 text-white" />
                  </div>
                  <div className="text-lg font-bold text-emerald-700">{property.bedrooms || 0}</div>
                  <div className="text-xs text-emerald-600 font-medium">Beds</div>
                </div>

                <div className="text-center p-3 bg-gradient-to-br from-blue-50 to-blue-100/50 rounded-xl border border-blue-200/50">
                  <div className="flex items-center justify-center w-8 h-8 bg-gradient-to-br from-blue-400 to-blue-500 rounded-full mx-auto mb-2">
                    <Bath className="h-4 w-4 text-white" />
                  </div>
                  <div className="text-lg font-bold text-blue-700">{property.bathrooms || 0}</div>
                  <div className="text-xs text-blue-600 font-medium">Baths</div>
                </div>

                <div className="text-center p-3 bg-gradient-to-br from-purple-50 to-purple-100/50 rounded-xl border border-purple-200/50">
                  <div className="flex items-center justify-center w-8 h-8 bg-gradient-to-br from-purple-400 to-purple-500 rounded-full mx-auto mb-2">
                    <Square className="h-4 w-4 text-white" />
                  </div>
                  <div className="text-lg font-bold text-purple-700">{property.area || 0}</div>
                  <div className="text-xs text-purple-600 font-medium">m²</div>
                </div>
              </div>

              <Button asChild className="w-full bg-gradient-to-r from-emerald-500 to-blue-500 hover:from-emerald-600 hover:to-blue-600 text-white border-0 rounded-xl py-3 font-semibold shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-[1.02] group">
                <Link href={`/properties/${property.id}`} className="flex items-center justify-center">
                  <span>View Details</span>
                  <Eye className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform duration-300" />
                </Link>
              </Button>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Modern Pagination */}
      <div className="flex justify-center mt-12">
        <div className="flex items-center space-x-3 bg-white/80 backdrop-blur-sm rounded-2xl p-2 shadow-lg border border-gray-200/50">
          <Button variant="outline" disabled className="rounded-xl border-gray-300/50 hover:bg-gradient-to-r hover:from-emerald-50 hover:to-blue-50 transition-all duration-300">
            Previous
          </Button>
          <Button className="bg-gradient-to-r from-emerald-500 to-blue-500 text-white border-0 rounded-xl shadow-md">
            1
          </Button>
          <Button variant="outline" className="rounded-xl border-gray-300/50 hover:bg-gradient-to-r hover:from-emerald-50 hover:to-blue-50 transition-all duration-300">
            2
          </Button>
          <Button variant="outline" className="rounded-xl border-gray-300/50 hover:bg-gradient-to-r hover:from-emerald-50 hover:to-blue-50 transition-all duration-300">
            3
          </Button>
          <Button variant="outline" className="rounded-xl border-gray-300/50 hover:bg-gradient-to-r hover:from-emerald-50 hover:to-blue-50 transition-all duration-300">
            Next
          </Button>
        </div>
      </div>
    </div>
  );
}
