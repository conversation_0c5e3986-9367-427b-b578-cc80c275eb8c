import { DashboardStats } from '@/components/features/dashboard/dashboard-stats';
import { RecentInquiries } from '@/components/features/dashboard/recent-inquiries';
import { QuickActions } from '@/components/features/dashboard/quick-actions';

export default function DashboardPage() {
  return (
    <div className="space-y-10">
      <div className="relative">
        <div className="absolute inset-0 bg-gradient-to-r from-italian-green/5 via-white/50 to-italian-red/5 rounded-2xl -z-10"></div>
        <div className="relative py-8">
          <h1 className="text-4xl lg:text-5xl font-bold text-gray-900 italian-flag-corner animate-slide-up mb-3">
            Dashboard
          </h1>
          <p className="text-xl text-gray-600 animate-fade-in-delay">
            Welcome back! Here&apos;s what&apos;s happening with your properties.
          </p>
        </div>
      </div>

      <DashboardStats />

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-10">
        <div className="lg:col-span-2">
          <RecentInquiries />
        </div>
        <div>
          <QuickActions />
        </div>
      </div>
    </div>
  );
}
