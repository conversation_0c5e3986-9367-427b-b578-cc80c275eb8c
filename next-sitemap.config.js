/** @type {import('next-sitemap').IConfig} */
module.exports = {
  siteUrl: process.env.SITE_URL || 'https://darden-pm.com',
  generateRobotsTxt: true,
  generateIndexSitemap: false,
  exclude: [
    '/admin/*',
    '/dashboard/*',
    '/auth/*',
    '/api/*',
    '/server-sitemap.xml',
  ],
  robotsTxtOptions: {
    policies: [
      {
        userAgent: '*',
        allow: '/',
        disallow: ['/admin/', '/dashboard/', '/auth/', '/api/'],
      },
    ],
    additionalSitemaps: [
      'https://darden-pm.com/server-sitemap.xml',
    ],
  },
  transform: async (config, path) => {
    // Custom priority and changefreq for different pages
    const customConfig = {
      loc: path,
      changefreq: 'daily',
      priority: 0.7,
      lastmod: new Date().toISOString(),
    }

    // Higher priority for important pages
    if (path === '/') {
      customConfig.priority = 1.0
      customConfig.changefreq = 'daily'
    } else if (path === '/properties') {
      customConfig.priority = 0.9
      customConfig.changefreq = 'daily'
    } else if (path === '/about' || path === '/contact' || path === '/services') {
      customConfig.priority = 0.8
      customConfig.changefreq = 'weekly'
    } else if (path.startsWith('/properties/')) {
      customConfig.priority = 0.8
      customConfig.changefreq = 'weekly'
    } else if (path === '/blog') {
      customConfig.priority = 0.7
      customConfig.changefreq = 'daily'
    }

    return customConfig
  },
}
