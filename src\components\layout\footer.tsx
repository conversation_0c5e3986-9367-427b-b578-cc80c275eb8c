import Link from 'next/link';
import { APP_CONFIG } from '@/constants/app';
import { Phone, Mail, MapPin, Facebook, Twitter, Instagram, Linkedin, Sparkles } from 'lucide-react';

export function Footer() {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="relative bg-gradient-to-br from-slate-900 via-gray-900 to-slate-900 text-white overflow-hidden">
      {/* Modern Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-emerald-500/10 to-blue-500/10 rounded-full blur-3xl"></div>
        <div className="absolute -bottom-40 -left-40 w-96 h-96 bg-gradient-to-tr from-purple-500/10 to-pink-500/10 rounded-full blur-3xl"></div>
      </div>

      {/* Decorative Grid Pattern */}
      <div className="absolute inset-0 opacity-[0.02]">
        <div className="absolute inset-0" style={{
          backgroundImage: `radial-gradient(circle at 1px 1px, white 1px, transparent 0)`,
          backgroundSize: '30px 30px'
        }}></div>
      </div>

      <div className="relative container mx-auto px-4 py-16">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Modern Company Info */}
          <div className="space-y-6">
            <div className="flex items-center space-x-3">
              <div className="relative">
                <div className="w-12 h-12 bg-gradient-to-br from-emerald-500 to-blue-500 rounded-2xl flex items-center justify-center shadow-lg">
                  <span className="text-white font-bold text-xl">D</span>
                </div>
                <div className="absolute -top-1 -right-1 w-4 h-4 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-full flex items-center justify-center">
                  <Sparkles className="h-2 w-2 text-white" />
                </div>
              </div>
              <div>
                <span className="font-bold text-xl bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
                  {APP_CONFIG.name.split(' ')[0]}
                </span>
                <div className="text-sm text-gray-400 -mt-1">
                  {APP_CONFIG.name.split(' ').slice(1).join(' ')}
                </div>
              </div>
            </div>
            <p className="text-gray-300 leading-relaxed">
              Premier real estate and property management services in Morocco.
              Your trusted partner for finding the perfect property with modern solutions.
            </p>
            <div className="flex space-x-3">
              <a href="#" className="w-10 h-10 bg-white/10 backdrop-blur-sm rounded-xl flex items-center justify-center text-gray-400 hover:text-white hover:bg-gradient-to-r hover:from-emerald-500 hover:to-blue-500 transition-all duration-300 transform hover:scale-110">
                <Facebook className="h-5 w-5" />
              </a>
              <a href="#" className="w-10 h-10 bg-white/10 backdrop-blur-sm rounded-xl flex items-center justify-center text-gray-400 hover:text-white hover:bg-gradient-to-r hover:from-emerald-500 hover:to-blue-500 transition-all duration-300 transform hover:scale-110">
                <Twitter className="h-5 w-5" />
              </a>
              <a href="#" className="w-10 h-10 bg-white/10 backdrop-blur-sm rounded-xl flex items-center justify-center text-gray-400 hover:text-white hover:bg-gradient-to-r hover:from-emerald-500 hover:to-blue-500 transition-all duration-300 transform hover:scale-110">
                <Instagram className="h-5 w-5" />
              </a>
              <a href="#" className="w-10 h-10 bg-white/10 backdrop-blur-sm rounded-xl flex items-center justify-center text-gray-400 hover:text-white hover:bg-gradient-to-r hover:from-emerald-500 hover:to-blue-500 transition-all duration-300 transform hover:scale-110">
                <Linkedin className="h-5 w-5" />
              </a>
            </div>
          </div>

          {/* Modern Quick Links */}
          <div className="space-y-6">
            <h3 className="font-bold text-lg bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">Quick Links</h3>
            <ul className="space-y-3">
              <li>
                <Link href="/properties" className="text-gray-300 hover:text-white hover:translate-x-1 transition-all duration-300 flex items-center group">
                  <span className="w-1 h-1 bg-emerald-400 rounded-full mr-3 group-hover:w-2 transition-all duration-300"></span>
                  Properties
                </Link>
              </li>
              <li>
                <Link href="/services" className="text-gray-300 hover:text-white hover:translate-x-1 transition-all duration-300 flex items-center group">
                  <span className="w-1 h-1 bg-emerald-400 rounded-full mr-3 group-hover:w-2 transition-all duration-300"></span>
                  Services
                </Link>
              </li>
              <li>
                <Link href="/about" className="text-gray-300 hover:text-white hover:translate-x-1 transition-all duration-300 flex items-center group">
                  <span className="w-1 h-1 bg-emerald-400 rounded-full mr-3 group-hover:w-2 transition-all duration-300"></span>
                  About Us
                </Link>
              </li>
              <li>
                <Link href="/career" className="text-gray-300 hover:text-white hover:translate-x-1 transition-all duration-300 flex items-center group">
                  <span className="w-1 h-1 bg-emerald-400 rounded-full mr-3 group-hover:w-2 transition-all duration-300"></span>
                  Career
                </Link>
              </li>
              <li>
                <Link href="/blog" className="text-gray-300 hover:text-white hover:translate-x-1 transition-all duration-300 flex items-center group">
                  <span className="w-1 h-1 bg-emerald-400 rounded-full mr-3 group-hover:w-2 transition-all duration-300"></span>
                  Blog
                </Link>
              </li>
              <li>
                <Link href="/contact" className="text-gray-300 hover:text-white hover:translate-x-1 transition-all duration-300 flex items-center group">
                  <span className="w-1 h-1 bg-emerald-400 rounded-full mr-3 group-hover:w-2 transition-all duration-300"></span>
                  Contact
                </Link>
              </li>
            </ul>
          </div>

          {/* Modern Property Types */}
          <div className="space-y-6">
            <h3 className="font-bold text-lg bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">Property Types</h3>
            <ul className="space-y-3">
              <li>
                <Link href="/properties?type=apartment" className="text-gray-300 hover:text-white hover:translate-x-1 transition-all duration-300 flex items-center group">
                  <span className="w-1 h-1 bg-blue-400 rounded-full mr-3 group-hover:w-2 transition-all duration-300"></span>
                  Apartments
                </Link>
              </li>
              <li>
                <Link href="/properties?type=villa" className="text-gray-300 hover:text-white hover:translate-x-1 transition-all duration-300 flex items-center group">
                  <span className="w-1 h-1 bg-blue-400 rounded-full mr-3 group-hover:w-2 transition-all duration-300"></span>
                  Villas
                </Link>
              </li>
              <li>
                <Link href="/properties?type=house" className="text-gray-300 hover:text-white hover:translate-x-1 transition-all duration-300 flex items-center group">
                  <span className="w-1 h-1 bg-blue-400 rounded-full mr-3 group-hover:w-2 transition-all duration-300"></span>
                  Houses
                </Link>
              </li>
              <li>
                <Link href="/properties?type=commercial" className="text-gray-300 hover:text-white hover:translate-x-1 transition-all duration-300 flex items-center group">
                  <span className="w-1 h-1 bg-blue-400 rounded-full mr-3 group-hover:w-2 transition-all duration-300"></span>
                  Commercial
                </Link>
              </li>
              <li>
                <Link href="/properties?type=land" className="text-gray-300 hover:text-white hover:translate-x-1 transition-all duration-300 flex items-center group">
                  <span className="w-1 h-1 bg-blue-400 rounded-full mr-3 group-hover:w-2 transition-all duration-300"></span>
                  Land
                </Link>
              </li>
            </ul>
          </div>

          {/* Modern Contact Info */}
          <div className="space-y-6">
            <h3 className="font-bold text-lg bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">Contact Info</h3>
            <div className="space-y-4">
              <div className="flex items-start space-x-3 group">
                <div className="flex items-center justify-center w-8 h-8 bg-white/10 backdrop-blur-sm rounded-lg group-hover:bg-gradient-to-r group-hover:from-emerald-500 group-hover:to-blue-500 transition-all duration-300">
                  <MapPin className="h-4 w-4 text-emerald-400 group-hover:text-white" />
                </div>
                <span className="text-gray-300 group-hover:text-white transition-colors">{APP_CONFIG.address}</span>
              </div>
              <div className="flex items-center space-x-3 group">
                <div className="flex items-center justify-center w-8 h-8 bg-white/10 backdrop-blur-sm rounded-lg group-hover:bg-gradient-to-r group-hover:from-emerald-500 group-hover:to-blue-500 transition-all duration-300">
                  <Phone className="h-4 w-4 text-emerald-400 group-hover:text-white" />
                </div>
                <span className="text-gray-300 group-hover:text-white transition-colors">{APP_CONFIG.phone}</span>
              </div>
              <div className="flex items-center space-x-3 group">
                <div className="flex items-center justify-center w-8 h-8 bg-white/10 backdrop-blur-sm rounded-lg group-hover:bg-gradient-to-r group-hover:from-emerald-500 group-hover:to-blue-500 transition-all duration-300">
                  <Mail className="h-4 w-4 text-emerald-400 group-hover:text-white" />
                </div>
                <span className="text-gray-300 group-hover:text-white transition-colors">{APP_CONFIG.email}</span>
              </div>
            </div>
          </div>
        </div>

        {/* Modern Bottom Bar */}
        <div className="border-t border-white/10 mt-12 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-6 md:space-y-0">
            <div className="text-center md:text-left">
              <p className="text-gray-400 text-sm mb-2">
                © {currentYear} {APP_CONFIG.name}. All rights reserved.
              </p>
              <p className="text-gray-500 text-xs">
                Made with ❤️ in Morocco
              </p>
            </div>
            <div className="flex flex-wrap justify-center gap-6 text-sm">
              <Link href="/privacy" className="text-gray-400 hover:text-white hover:translate-y-[-1px] transition-all duration-300 relative group">
                Privacy Policy
                <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-emerald-400 to-blue-400 group-hover:w-full transition-all duration-300"></span>
              </Link>
              <Link href="/terms" className="text-gray-400 hover:text-white hover:translate-y-[-1px] transition-all duration-300 relative group">
                Terms of Service
                <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-emerald-400 to-blue-400 group-hover:w-full transition-all duration-300"></span>
              </Link>
              <Link href="/sitemap" className="text-gray-400 hover:text-white hover:translate-y-[-1px] transition-all duration-300 relative group">
                Sitemap
                <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-emerald-400 to-blue-400 group-hover:w-full transition-all duration-300"></span>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}
