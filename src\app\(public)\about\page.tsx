import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { 
  Users, 
  Award, 
  Globe, 
  Heart,
  Target,
  Eye,
  Linkedin,
  Mail
} from 'lucide-react';
import Link from 'next/link';

const stats = [
  { label: 'Years of Experience', value: '15+' },
  { label: 'Properties Sold', value: '500+' },
  { label: 'Happy Clients', value: '1000+' },
  { label: 'Cities Covered', value: '12' },
];

const values = [
  {
    icon: Heart,
    title: 'Client-Centric',
    description: 'We put our clients first, ensuring their needs and goals are our top priority.',
  },
  {
    icon: Award,
    title: 'Excellence',
    description: 'We strive for excellence in every transaction and interaction.',
  },
  {
    icon: Users,
    title: 'Integrity',
    description: 'We conduct business with honesty, transparency, and ethical practices.',
  },
  {
    icon: Globe,
    title: 'Innovation',
    description: 'We embrace technology and innovative solutions to serve our clients better.',
  },
];

const team = [
  {
    name: '<PERSON>',
    role: 'Founder & CEO',
    bio: 'With over 15 years in Moroccan real estate, <PERSON> founded Darden PM to revolutionize property services.',
    image: '/team/ahmed.jpg',
  },
  {
    name: '<PERSON><PERSON>',
    role: 'Head of Sales',
    bio: 'Fatima leads our sales team with expertise in luxury properties and international clients.',
    image: '/team/fatima.jpg',
  },
  {
    name: 'Youssef Mansouri',
    role: 'Property Manager',
    bio: 'Youssef oversees our property management division with a focus on client satisfaction.',
    image: '/team/youssef.jpg',
  },
  {
    name: 'Sarah El Fassi',
    role: 'Marketing Director',
    bio: 'Sarah drives our marketing initiatives and digital presence across Morocco.',
    image: '/team/sarah.jpg',
  },
];

export default function AboutPage() {
  return (
    <div className="min-h-screen relative overflow-hidden">
      {/* Modern Background with Gradients */}
      <div className="absolute inset-0 bg-gradient-to-br from-slate-50 via-white to-emerald-50/30">
        <div className="absolute inset-0 bg-gradient-to-tr from-blue-50/20 via-transparent to-purple-50/20"></div>
      </div>

      {/* Animated Background Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-emerald-200/20 to-blue-200/20 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute -bottom-40 -left-40 w-96 h-96 bg-gradient-to-tr from-purple-200/15 to-pink-200/15 rounded-full blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute top-1/3 right-1/4 w-64 h-64 bg-gradient-to-r from-yellow-200/10 to-orange-200/10 rounded-full blur-2xl animate-float-slow"></div>
      </div>

      {/* Decorative Grid Pattern */}
      <div className="absolute inset-0 opacity-[0.02]">
        <div className="absolute inset-0" style={{
          backgroundImage: `radial-gradient(circle at 1px 1px, rgb(59 130 246) 1px, transparent 0)`,
          backgroundSize: '40px 40px'
        }}></div>
      </div>

      <div className="relative py-16">
        <div className="container mx-auto px-4">
          {/* Modern Hero Section */}
          <div className="text-center mb-20">
            <div className="inline-flex items-center gap-2 bg-white/80 backdrop-blur-sm border border-emerald-200/50 rounded-full px-6 py-2 mb-8 animate-fade-in">
              <div className="w-2 h-2 bg-emerald-400 rounded-full animate-pulse"></div>
              <span className="text-emerald-700 font-medium">About Us</span>
            </div>

            <h1 className="text-5xl lg:text-7xl font-bold mb-8 animate-slide-up">
              <span className="bg-gradient-to-r from-gray-900 via-emerald-700 to-blue-700 bg-clip-text text-transparent">
                Your Trusted
              </span>
              <br />
              <span className="bg-gradient-to-r from-emerald-500 via-blue-500 to-purple-500 bg-clip-text text-transparent">
                Real Estate Partner
              </span>
              <br />
              <span className="text-gray-700 text-4xl lg:text-5xl">
                in Morocco
              </span>
            </h1>

            <p className="text-xl lg:text-2xl text-gray-600 max-w-4xl mx-auto leading-relaxed animate-slide-up-delayed">
              Since 2009, Darden Property & Management has been helping clients navigate
              Morocco's dynamic real estate market with expertise, integrity, and innovation.
              We're not just a company—we're your partners in success.
            </p>
          </div>

          {/* Modern Stats */}
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-8 mb-24">
            {stats.map((stat, index) => {
              const gradients = [
                'from-emerald-500 to-teal-500',
                'from-blue-500 to-indigo-500',
                'from-purple-500 to-pink-500',
                'from-orange-500 to-red-500'
              ];
              const bgGradients = [
                'from-emerald-50 to-teal-50',
                'from-blue-50 to-indigo-50',
                'from-purple-50 to-pink-50',
                'from-orange-50 to-red-50'
              ];

              return (
                <div
                  key={index}
                  className="text-center p-8 bg-white/80 backdrop-blur-sm rounded-3xl shadow-xl border border-gray-200/50 hover:shadow-2xl transition-all duration-500 transform hover:scale-105 animate-fade-in"
                  style={{ animationDelay: `${index * 0.1}s` }}
                >
                  <div className={`text-5xl lg:text-6xl font-bold bg-gradient-to-r ${gradients[index]} bg-clip-text text-transparent mb-4`}>
                    {stat.value}
                  </div>
                  <div className={`inline-flex items-center bg-gradient-to-r ${bgGradients[index]} px-4 py-2 rounded-xl border border-gray-200/50`}>
                    <span className="text-gray-700 font-medium">{stat.label}</span>
                  </div>
                </div>
              );
            })}
          </div>

          {/* Modern Story Section */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center mb-24">
            <div className="animate-slide-up">
              <div className="inline-flex items-center gap-2 bg-gradient-to-r from-emerald-100 to-blue-100 rounded-full px-6 py-2 mb-6">
                <div className="w-2 h-2 bg-gradient-to-r from-emerald-500 to-blue-500 rounded-full animate-pulse"></div>
                <span className="text-emerald-700 font-medium">Our Journey</span>
              </div>

              <h2 className="text-4xl lg:text-5xl font-bold mb-8">
                <span className="bg-gradient-to-r from-gray-900 via-emerald-700 to-blue-700 bg-clip-text text-transparent">
                  Our Story
                </span>
              </h2>

              <div className="space-y-6 text-gray-700 text-lg leading-relaxed">
                <p className="p-6 bg-gradient-to-r from-emerald-50 to-teal-50 rounded-2xl border border-emerald-200/50">
                  <strong className="text-emerald-700">Founded in 2009</strong> by Ahmed Benali, Darden Property & Management began as a
                  small real estate agency in Casablanca with a simple mission: to provide
                  exceptional service to property buyers, sellers, and investors in Morocco.
                </p>
                <p className="p-6 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl border border-blue-200/50">
                  Over the years, we've grown into one of <strong className="text-blue-700">Morocco's most trusted real estate
                  companies</strong>, expanding our services to include property management, investment
                  consulting, and comprehensive real estate solutions across 12 major cities.
                </p>
                <p className="p-6 bg-gradient-to-r from-purple-50 to-pink-50 rounded-2xl border border-purple-200/50">
                  Today, as Morocco prepares to co-host the <strong className="text-purple-700">FIFA World Cup 2030</strong> and the CAF
                  Africa Cup of Nations 2025, we're at the forefront of the country's real
                  estate boom, helping clients capitalize on unprecedented opportunities.
                </p>
              </div>
            </div>

            <div className="animate-slide-up-delayed">
              <div className="aspect-[4/3] bg-gradient-to-br from-emerald-100 via-blue-50 to-purple-100 rounded-3xl flex items-center justify-center shadow-2xl border border-gray-200/50 overflow-hidden">
                <div className="text-center">
                  <div className="w-24 h-24 bg-gradient-to-br from-emerald-400 to-blue-500 rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg">
                    <Users className="h-12 w-12 text-white" />
                  </div>
                  <p className="text-xl font-semibold text-gray-600 mb-2">Company Story</p>
                  <p className="text-gray-500">Building Dreams Since 2009</p>
                </div>
              </div>
            </div>
          </div>

          {/* Modern Mission & Vision */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-24">
            <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-xl rounded-3xl overflow-hidden hover:shadow-2xl transition-all duration-500 transform hover:scale-[1.02] animate-slide-up">
              <CardContent className="p-10">
                <div className="flex items-center mb-6">
                  <div className="w-16 h-16 bg-gradient-to-br from-emerald-400 to-teal-500 rounded-2xl flex items-center justify-center mr-4 shadow-lg">
                    <Target className="h-8 w-8 text-white" />
                  </div>
                  <h3 className="text-3xl font-bold bg-gradient-to-r from-emerald-600 to-teal-600 bg-clip-text text-transparent">
                    Our Mission
                  </h3>
                </div>
                <p className="text-gray-700 text-lg leading-relaxed">
                  To empower our clients with expert knowledge, innovative solutions, and
                  personalized service that transforms their real estate dreams into reality
                  while contributing to Morocco's economic growth and development.
                </p>
              </CardContent>
            </Card>

            <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-xl rounded-3xl overflow-hidden hover:shadow-2xl transition-all duration-500 transform hover:scale-[1.02] animate-slide-up-delayed">
              <CardContent className="p-10">
                <div className="flex items-center mb-6">
                  <div className="w-16 h-16 bg-gradient-to-br from-blue-400 to-indigo-500 rounded-2xl flex items-center justify-center mr-4 shadow-lg">
                    <Eye className="h-8 w-8 text-white" />
                  </div>
                  <h3 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
                    Our Vision
                  </h3>
                </div>
                <p className="text-gray-700 text-lg leading-relaxed">
                  To be Morocco's leading real estate company, recognized for our integrity,
                  innovation, and commitment to excellence, while setting new standards for
                  the industry across North Africa and beyond.
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Modern Values */}
          <div className="mb-24">
            <div className="text-center mb-16">
              <div className="inline-flex items-center gap-2 bg-gradient-to-r from-purple-100 to-pink-100 rounded-full px-6 py-2 mb-6">
                <div className="w-2 h-2 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full animate-pulse"></div>
                <span className="text-purple-700 font-medium">Our Values</span>
              </div>

              <h2 className="text-4xl lg:text-5xl font-bold mb-6">
                <span className="bg-gradient-to-r from-gray-900 via-purple-700 to-pink-700 bg-clip-text text-transparent">
                  What Drives Us
                </span>
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
                These core values guide everything we do and shape our relationships
                with clients, partners, and communities across Morocco and beyond.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              {values.map((value, index) => {
                const Icon = value.icon;
                const gradients = [
                  'from-red-500 to-pink-500',
                  'from-yellow-500 to-orange-500',
                  'from-green-500 to-emerald-500',
                  'from-blue-500 to-indigo-500'
                ];
                const bgGradients = [
                  'from-red-50 to-pink-50',
                  'from-yellow-50 to-orange-50',
                  'from-green-50 to-emerald-50',
                  'from-blue-50 to-indigo-50'
                ];

                return (
                  <Card
                    key={index}
                    className="text-center bg-white/80 backdrop-blur-sm border-0 shadow-xl rounded-3xl overflow-hidden hover:shadow-2xl transition-all duration-500 transform hover:scale-105 animate-fade-in"
                    style={{ animationDelay: `${index * 0.1}s` }}
                  >
                    <CardContent className="p-8">
                      <div className={`w-20 h-20 bg-gradient-to-br ${gradients[index]} rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg hover:scale-110 transition-transform duration-300`}>
                        <Icon className="h-10 w-10 text-white" />
                      </div>
                      <h3 className="text-2xl font-bold text-gray-900 mb-4 hover:text-purple-600 transition-colors duration-300">
                        {value.title}
                      </h3>
                      <p className="text-gray-600 leading-relaxed">
                        {value.description}
                      </p>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          </div>

          {/* Modern Team */}
          <div className="mb-24">
            <div className="text-center mb-16">
              <div className="inline-flex items-center gap-2 bg-gradient-to-r from-emerald-100 to-teal-100 rounded-full px-6 py-2 mb-6">
                <div className="w-2 h-2 bg-gradient-to-r from-emerald-500 to-teal-500 rounded-full animate-pulse"></div>
                <span className="text-emerald-700 font-medium">Our Team</span>
              </div>

              <h2 className="text-4xl lg:text-5xl font-bold mb-6">
                <span className="bg-gradient-to-r from-gray-900 via-emerald-700 to-teal-700 bg-clip-text text-transparent">
                  Meet Our Experts
                </span>
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
                Our experienced team of real estate professionals is dedicated to
                providing exceptional service, expertise, and personalized attention to every client.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              {team.map((member, index) => {
                const gradients = [
                  'from-emerald-400 to-teal-500',
                  'from-blue-400 to-indigo-500',
                  'from-purple-400 to-pink-500',
                  'from-orange-400 to-red-500'
                ];

                return (
                  <Card
                    key={index}
                    className="text-center bg-white/80 backdrop-blur-sm border-0 shadow-xl rounded-3xl overflow-hidden hover:shadow-2xl transition-all duration-500 transform hover:scale-105 animate-fade-in group"
                    style={{ animationDelay: `${index * 0.1}s` }}
                  >
                    <CardContent className="p-8">
                      <div className={`w-28 h-28 bg-gradient-to-br ${gradients[index]} rounded-2xl mx-auto mb-6 flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300`}>
                        <Users className="h-14 w-14 text-white" />
                      </div>
                      <h3 className="text-xl font-bold text-gray-900 mb-2 group-hover:text-emerald-600 transition-colors duration-300">
                        {member.name}
                      </h3>
                      <div className={`inline-flex items-center bg-gradient-to-r ${gradients[index]} text-white px-4 py-1 rounded-xl font-medium text-sm mb-4 shadow-sm`}>
                        {member.role}
                      </div>
                      <p className="text-gray-600 text-sm mb-6 leading-relaxed">
                        {member.bio}
                      </p>
                      <div className="flex justify-center space-x-3">
                        <Button size="sm" className={`bg-gradient-to-r ${gradients[index]} text-white border-0 rounded-xl hover:shadow-lg transition-all duration-300 transform hover:scale-105`}>
                          <Linkedin className="h-4 w-4" />
                        </Button>
                        <Button size="sm" className="bg-white/50 backdrop-blur-sm border-gray-300/50 text-gray-700 hover:bg-gray-100 rounded-xl transition-all duration-300 transform hover:scale-105">
                          <Mail className="h-4 w-4" />
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          </div>

          {/* Modern CTA */}
          <div className="relative text-center bg-gradient-to-br from-emerald-500 via-blue-500 to-purple-500 rounded-3xl p-8 lg:p-16 text-white overflow-hidden shadow-2xl">
            {/* Background Pattern */}
            <div className="absolute inset-0 opacity-10">
              <div className="absolute inset-0" style={{
                backgroundImage: `radial-gradient(circle at 1px 1px, white 1px, transparent 0)`,
                backgroundSize: '30px 30px'
              }}></div>
            </div>

            {/* Floating Elements */}
            <div className="absolute top-4 left-4 w-8 h-8 bg-white/20 rounded-full animate-float"></div>
            <div className="absolute top-8 right-8 w-6 h-6 bg-white/20 rounded-full animate-float-delayed"></div>
            <div className="absolute bottom-8 left-8 w-4 h-4 bg-white/20 rounded-full animate-float-slow"></div>

            <div className="relative">
              <h2 className="text-4xl lg:text-6xl font-bold mb-6">
                Ready to Work With Us?
              </h2>
              <p className="text-xl lg:text-2xl mb-10 opacity-90 max-w-3xl mx-auto leading-relaxed">
                Join thousands of satisfied clients who have trusted us with their
                real estate needs. Let's make your property dreams a reality with our expert guidance.
              </p>
              <div className="flex flex-col sm:flex-row gap-6 justify-center">
                <Button size="lg" className="bg-white text-emerald-600 hover:bg-gray-100 px-8 py-4 rounded-xl font-semibold shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105" asChild>
                  <Link href="/contact">Contact Us Today</Link>
                </Button>
                <Button size="lg" className="bg-white/20 backdrop-blur-sm border-white/30 text-white hover:bg-white hover:text-emerald-600 px-8 py-4 rounded-xl font-semibold transition-all duration-300 transform hover:scale-105" asChild>
                  <Link href="/properties">View Properties</Link>
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
