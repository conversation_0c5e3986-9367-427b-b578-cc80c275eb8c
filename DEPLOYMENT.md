# Darden Property Management - Production Deployment Guide

This guide covers the deployment of the Darden Property Management application to production.

## Prerequisites

- Node.js 18+ installed
- Supabase project set up
- Domain name configured
- SSL certificate (handled by hosting provider)

## Environment Configuration

### 1. Copy Environment Variables

```bash
cp .env.example .env.local
```

### 2. Configure Required Variables

Update `.env.local` with your production values:

```env
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

# Application Configuration
NEXT_PUBLIC_APP_URL=https://your-domain.com
NEXT_PUBLIC_APP_NAME="Darden Property & Management"
NODE_ENV=production

# Security
NEXTAUTH_SECRET=your_secure_random_string
NEXTAUTH_URL=https://your-domain.com

# Email Configuration
SMTP_HOST=your_smtp_host
SMTP_PORT=587
SMTP_USER=your_smtp_user
SMTP_PASS=your_smtp_password
SMTP_FROM=<EMAIL>
```

## Supabase Setup

### 1. Database Schema

Run the following SQL in your Supabase SQL editor:

```sql
-- Enable RLS
ALTER TABLE properties ENABLE ROW LEVEL SECURITY;
ALTER TABLE users ENABLE ROW LEVEL SECURITY;

-- Create properties table
CREATE TABLE IF NOT EXISTS properties (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  title VARCHAR(255) NOT NULL,
  description TEXT,
  price DECIMAL(12,2) NOT NULL,
  currency VARCHAR(3) DEFAULT 'MAD',
  property_type VARCHAR(50) NOT NULL,
  status VARCHAR(20) DEFAULT 'available',
  bedrooms INTEGER,
  bathrooms INTEGER,
  area DECIMAL(10,2),
  address TEXT,
  city VARCHAR(100),
  region VARCHAR(100),
  country VARCHAR(100) DEFAULT 'Morocco',
  features TEXT[],
  images TEXT[],
  virtual_tour_url TEXT,
  created_by UUID REFERENCES auth.users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create RLS policies
CREATE POLICY "Properties are viewable by everyone" ON properties
  FOR SELECT USING (true);

CREATE POLICY "Users can insert their own properties" ON properties
  FOR INSERT WITH CHECK (auth.uid() = created_by);

CREATE POLICY "Users can update their own properties" ON properties
  FOR UPDATE USING (auth.uid() = created_by);

CREATE POLICY "Users can delete their own properties" ON properties
  FOR DELETE USING (auth.uid() = created_by);

-- Create storage bucket for property images
INSERT INTO storage.buckets (id, name, public) VALUES ('property-images', 'property-images', true);

-- Create storage policies
CREATE POLICY "Property images are publicly accessible" ON storage.objects
  FOR SELECT USING (bucket_id = 'property-images');

CREATE POLICY "Authenticated users can upload property images" ON storage.objects
  FOR INSERT WITH CHECK (bucket_id = 'property-images' AND auth.role() = 'authenticated');

CREATE POLICY "Users can update their own property images" ON storage.objects
  FOR UPDATE USING (bucket_id = 'property-images' AND auth.uid()::text = (storage.foldername(name))[1]);

CREATE POLICY "Users can delete their own property images" ON storage.objects
  FOR DELETE USING (bucket_id = 'property-images' AND auth.uid()::text = (storage.foldername(name))[1]);
```

### 2. Authentication Setup

Configure authentication providers in Supabase Dashboard:
- Enable Email/Password authentication
- Configure email templates
- Set up OAuth providers if needed

## Deployment Options

### Option 1: Vercel (Recommended)

1. **Connect Repository**
   ```bash
   npm install -g vercel
   vercel login
   vercel
   ```

2. **Configure Environment Variables**
   - Go to Vercel Dashboard → Project → Settings → Environment Variables
   - Add all variables from your `.env.local`

3. **Deploy**
   ```bash
   vercel --prod
   ```

### Option 2: Netlify

1. **Build Command**: `npm run build`
2. **Publish Directory**: `.next`
3. **Environment Variables**: Add in Netlify Dashboard

### Option 3: Docker

1. **Create Dockerfile**
   ```dockerfile
   FROM node:18-alpine AS deps
   WORKDIR /app
   COPY package*.json ./
   RUN npm ci --only=production

   FROM node:18-alpine AS builder
   WORKDIR /app
   COPY . .
   COPY --from=deps /app/node_modules ./node_modules
   RUN npm run build

   FROM node:18-alpine AS runner
   WORKDIR /app
   ENV NODE_ENV production
   COPY --from=builder /app/public ./public
   COPY --from=builder /app/.next ./.next
   COPY --from=builder /app/node_modules ./node_modules
   COPY --from=builder /app/package.json ./package.json

   EXPOSE 3000
   CMD ["npm", "start"]
   ```

2. **Build and Run**
   ```bash
   docker build -t darden-pm .
   docker run -p 3000:3000 darden-pm
   ```

## Post-Deployment Checklist

### 1. Verify Functionality
- [ ] Homepage loads correctly
- [ ] Property search works
- [ ] User authentication works
- [ ] Property creation/editing works
- [ ] Image uploads work
- [ ] Email notifications work (if configured)

### 2. Performance Optimization
- [ ] Enable compression (gzip/brotli)
- [ ] Configure CDN for static assets
- [ ] Set up image optimization
- [ ] Enable caching headers

### 3. Security
- [ ] HTTPS is enforced
- [ ] Security headers are configured
- [ ] Rate limiting is active
- [ ] CORS is properly configured

### 4. Monitoring
- [ ] Error tracking is set up (Sentry recommended)
- [ ] Analytics are configured
- [ ] Uptime monitoring is active
- [ ] Performance monitoring is enabled

### 5. SEO
- [ ] Sitemap is generated
- [ ] Meta tags are optimized
- [ ] Open Graph tags are set
- [ ] Robots.txt is configured

## Environment-Specific Configurations

### Production
- Enable error tracking
- Configure monitoring
- Set up automated backups
- Enable rate limiting

### Staging
- Use separate Supabase project
- Enable debug logging
- Test email configurations

## Troubleshooting

### Common Issues

1. **Build Failures**
   - Check Node.js version compatibility
   - Verify all environment variables are set
   - Clear node_modules and reinstall

2. **Database Connection Issues**
   - Verify Supabase URL and keys
   - Check RLS policies
   - Ensure database schema is up to date

3. **Image Upload Issues**
   - Verify storage bucket exists
   - Check storage policies
   - Confirm file size limits

4. **Authentication Issues**
   - Verify NEXTAUTH_SECRET is set
   - Check NEXTAUTH_URL matches domain
   - Confirm Supabase auth settings

### Logs and Debugging

- Check Vercel/Netlify function logs
- Monitor Supabase logs
- Use browser developer tools
- Check network requests

## Maintenance

### Regular Tasks
- Monitor error rates
- Update dependencies
- Backup database
- Review performance metrics
- Update content and images

### Security Updates
- Keep Next.js updated
- Update Supabase client
- Monitor security advisories
- Review access logs

## Support

For deployment issues:
1. Check this guide first
2. Review error logs
3. Check Supabase documentation
4. Contact development team

---

**Note**: This guide assumes familiarity with web deployment concepts. For additional help, consult the official documentation for your chosen hosting platform.
