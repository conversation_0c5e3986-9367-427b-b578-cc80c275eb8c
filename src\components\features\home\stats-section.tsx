import { Trophy, Users, Building, Globe } from 'lucide-react';

const stats = [
  {
    icon: Building,
    value: '500+',
    label: 'Properties Listed',
    description: 'Across Morocco',
  },
  {
    icon: Users,
    value: '1000+',
    label: 'Happy Clients',
    description: 'Satisfied customers',
  },
  {
    icon: Trophy,
    value: '15+',
    label: 'Years Experience',
    description: 'In real estate',
  },
  {
    icon: Globe,
    value: '12',
    label: 'Cities Covered',
    description: 'Major Moroccan cities',
  },
];

const highlights = [
  {
    title: 'FIFA World Cup 2030',
    description: 'Morocco co-hosting brings unprecedented growth opportunities',
    impact: '+25% property value increase expected',
  },
  {
    title: 'CAF 2025',
    description: 'Africa Cup of Nations boosting tourism and real estate',
    impact: 'New infrastructure development',
  },
  {
    title: 'Tourism Growth',
    description: 'Record-breaking visitor numbers driving demand',
    impact: '12M+ tourists annually',
  },
];

export function StatsSection() {
  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        {/* Stats Grid */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
          {stats.map((stat, index) => {
            const Icon = stat.icon;
            return (
              <div key={index} className="text-center">
                <div className="inline-flex items-center justify-center w-16 h-16 bg-primary/10 rounded-full mb-4">
                  <Icon className="h-8 w-8 text-primary" />
                </div>
                <div className="text-3xl lg:text-4xl font-bold text-gray-900 mb-2">
                  {stat.value}
                </div>
                <div className="text-lg font-semibold text-gray-700 mb-1">
                  {stat.label}
                </div>
                <div className="text-sm text-gray-600">
                  {stat.description}
                </div>
              </div>
            );
          })}
        </div>

        {/* Morocco Highlights */}
        <div className="bg-gradient-to-r from-primary/5 to-primary/10 rounded-2xl p-8 lg:p-12">
          <div className="text-center mb-12">
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
              Morocco&apos;s Real Estate Boom
            </h2>
            <p className="text-gray-600 max-w-3xl mx-auto">
              With major international events and growing tourism, Morocco&apos;s real estate
              market is experiencing unprecedented growth and investment opportunities.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {highlights.map((highlight, index) => (
              <div key={index} className="bg-white rounded-lg p-6 shadow-sm">
                <h3 className="text-xl font-semibold text-gray-900 mb-3">
                  {highlight.title}
                </h3>
                <p className="text-gray-600 mb-4">
                  {highlight.description}
                </p>
                <div className="text-sm font-medium text-primary">
                  {highlight.impact}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}
