'use client';

import { useState, useEffect } from 'react';
import { supabase } from '@/lib/supabase/client';

interface PropertyStats {
  totalProperties: number;
  availableProperties: number;
  soldProperties: number;
  rentedProperties: number;
  pendingProperties: number;
  totalValue: number;
  averagePrice: number;
  recentListings: number;
}

export function usePropertyStats() {
  const [stats, setStats] = useState<PropertyStats>({
    totalProperties: 0,
    availableProperties: 0,
    soldProperties: 0,
    rentedProperties: 0,
    pendingProperties: 0,
    totalValue: 0,
    averagePrice: 0,
    recentListings: 0,
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchStats = async () => {
      try {
        setLoading(true);
        setError(null);

        // Get total properties count and status breakdown
        const { data: properties, error: propertiesError } = await supabase
          .from('properties')
          .select('status, price, created_at');

        if (propertiesError) {
          throw propertiesError;
        }

        if (!properties) {
          throw new Error('No data received');
        }

        // Calculate stats
        const totalProperties = properties.length;
        const availableProperties = properties.filter(p => p.status === 'available').length;
        const soldProperties = properties.filter(p => p.status === 'sold').length;
        const rentedProperties = properties.filter(p => p.status === 'rented').length;
        const pendingProperties = properties.filter(p => p.status === 'pending').length;
        
        const totalValue = properties.reduce((sum, p) => sum + (p.price || 0), 0);
        const averagePrice = totalProperties > 0 ? totalValue / totalProperties : 0;
        
        // Recent listings (last 30 days)
        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
        const recentListings = properties.filter(p => 
          new Date(p.created_at) >= thirtyDaysAgo
        ).length;

        setStats({
          totalProperties,
          availableProperties,
          soldProperties,
          rentedProperties,
          pendingProperties,
          totalValue,
          averagePrice,
          recentListings,
        });
      } catch (err) {
        console.error('Error fetching property stats:', err);
        setError(err instanceof Error ? err.message : 'Failed to fetch property stats');
      } finally {
        setLoading(false);
      }
    };

    fetchStats();
  }, []);

  return { stats, loading, error };
}

// Hook for property analytics
export function usePropertyAnalytics(timeRange: '7d' | '30d' | '90d' | '1y' = '30d') {
  const [analytics, setAnalytics] = useState({
    newListings: 0,
    soldProperties: 0,
    averageDaysOnMarket: 0,
    priceChanges: 0,
    viewsGrowth: 0,
    inquiriesGrowth: 0,
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchAnalytics = async () => {
      try {
        setLoading(true);
        setError(null);

        // Calculate date range
        const endDate = new Date();
        const startDate = new Date();
        
        switch (timeRange) {
          case '7d':
            startDate.setDate(endDate.getDate() - 7);
            break;
          case '30d':
            startDate.setDate(endDate.getDate() - 30);
            break;
          case '90d':
            startDate.setDate(endDate.getDate() - 90);
            break;
          case '1y':
            startDate.setFullYear(endDate.getFullYear() - 1);
            break;
        }

        const { data: properties, error: propertiesError } = await supabase
          .from('properties')
          .select('*')
          .gte('created_at', startDate.toISOString())
          .lte('created_at', endDate.toISOString());

        if (propertiesError) {
          throw propertiesError;
        }

        // Calculate analytics (simplified for now)
        const newListings = properties?.length || 0;
        const soldProperties = properties?.filter(p => p.status === 'sold').length || 0;
        
        setAnalytics({
          newListings,
          soldProperties,
          averageDaysOnMarket: 0, // Would need additional tracking
          priceChanges: 0, // Would need price history
          viewsGrowth: 0, // Would need view tracking
          inquiriesGrowth: 0, // Would need inquiry tracking
        });
      } catch (err) {
        console.error('Error fetching property analytics:', err);
        setError(err instanceof Error ? err.message : 'Failed to fetch analytics');
      } finally {
        setLoading(false);
      }
    };

    fetchAnalytics();
  }, [timeRange]);

  return { analytics, loading, error };
}
