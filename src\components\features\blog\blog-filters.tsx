import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Search } from 'lucide-react';

const categories = [
  'All Categories',
  'Market Analysis',
  'Investment',
  'Tourism',
  'Investment Guide',
  'Legal Guide',
  'Luxury Market',
  'Property Tips',
  'Morocco News',
];

const popularTags = [
  'FIFA 2030',
  'CAF 2025',
  'Casablanca',
  'Marrakech',
  'Investment',
  'Tourism',
  'Property Laws',
  'Market Trends',
  'Luxury Properties',
  'Foreign Buyers',
];

const recentPosts = [
  {
    title: 'Morocco Real Estate Market Trends 2024',
    date: '2024-01-15',
  },
  {
    title: 'FIFA World Cup 2030: Impact on Property Market',
    date: '2024-01-10',
  },
  {
    title: 'CAF 2025: Boosting Tourism and Real Estate',
    date: '2024-01-08',
  },
];

export function BlogFilters() {
  return (
    <div className="space-y-8">
      {/* Search */}
      <Card className="bg-gradient-to-br from-white to-gray-50 border-0 shadow-xl hover:shadow-2xl transition-all duration-500 hover-lift">
        <CardHeader className="pb-4">
          <CardTitle className="text-xl font-bold text-gray-900 italian-flag-corner">
            Search Articles
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="relative">
            <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-italian-green h-5 w-5" />
            <Input
              placeholder="Search articles..."
              className="pl-12 h-12 border-2 border-gray-200 focus:border-italian-green rounded-xl bg-white shadow-sm"
            />
          </div>
        </CardContent>
      </Card>

      {/* Categories */}
      <Card className="bg-gradient-to-br from-white to-gray-50 border-0 shadow-xl hover:shadow-2xl transition-all duration-500 hover-lift">
        <CardHeader className="pb-4">
          <CardTitle className="text-xl font-bold text-gray-900 italian-flag-corner">
            Categories
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {categories.map((category, index) => (
              <button
                key={category}
                className="block w-full text-left px-4 py-3 text-sm font-medium text-gray-700 hover:bg-gradient-to-r hover:from-italian-green hover:to-italian-red hover:text-white rounded-xl transition-all duration-300 hover-lift animate-fade-in"
                style={{ animationDelay: `${index * 50}ms` }}
              >
                {category}
              </button>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Popular Tags */}
      <Card className="bg-gradient-to-br from-white to-gray-50 border-0 shadow-xl hover:shadow-2xl transition-all duration-500 hover-lift">
        <CardHeader className="pb-4">
          <CardTitle className="text-xl font-bold text-gray-900 italian-flag-corner">
            Popular Tags
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-3">
            {popularTags.map((tag, index) => (
              <Button
                key={tag}
                variant="outline"
                size="sm"
                className="text-sm border-2 border-gray-200 hover:border-italian-green hover:bg-italian-green hover:text-white transition-all duration-300 rounded-lg px-3 py-2 animate-scale-in"
                style={{ animationDelay: `${index * 50}ms` }}
              >
                {tag}
              </Button>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Recent Posts */}
      <Card className="bg-gradient-to-br from-white to-gray-50 border-0 shadow-xl hover:shadow-2xl transition-all duration-500 hover-lift">
        <CardHeader className="pb-4">
          <CardTitle className="text-xl font-bold text-gray-900 italian-flag-corner">
            Recent Posts
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-5">
            {recentPosts.map((post, index) => (
              <div key={index} className="border-b border-gray-200 last:border-b-0 pb-4 last:pb-0 hover:bg-gradient-to-r hover:from-gray-50 hover:to-white p-3 rounded-lg transition-all duration-300 animate-fade-in" style={{ animationDelay: `${index * 100}ms` }}>
                <h4 className="text-sm font-bold text-gray-900 mb-2 line-clamp-2 hover:text-italian-green transition-colors duration-300 cursor-pointer">
                  {post.title}
                </h4>
                <p className="text-xs text-gray-500 font-medium">
                  {new Date(post.date).toLocaleDateString()}
                </p>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Newsletter */}
      <Card className="bg-gradient-to-br from-italian-green/5 to-italian-red/5 border-0 shadow-xl hover:shadow-2xl transition-all duration-500 hover-lift">
        <CardHeader className="pb-4">
          <CardTitle className="text-xl font-bold text-gray-900 italian-flag-corner">
            Newsletter
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-gray-600 mb-6 leading-relaxed">
            Subscribe to our newsletter for the latest real estate insights and market updates.
          </p>
          <div className="space-y-4">
            <Input
              placeholder="Your email address"
              type="email"
              className="h-12 border-2 border-gray-200 focus:border-italian-green rounded-xl bg-white shadow-sm"
            />
            <Button className="w-full bg-gradient-to-r from-italian-green to-italian-red hover:from-italian-red hover:to-italian-green transition-all duration-300 border-0 py-3 text-lg font-semibold shadow-lg hover:shadow-xl">
              Subscribe
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
