import { NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

// Create admin client to manage policies
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function POST() {
  try {
    console.log('Fixing RLS policies...');
    
    // Drop the problematic policy and create a simpler one
    const { error: dropError } = await supabaseAdmin.rpc('exec_sql', {
      sql: `
        DROP POLICY IF EXISTS "Anyone can view published properties" ON properties;
        DROP POLICY IF EXISTS "Admins and sales persons can view all properties" ON properties;
        
        CREATE POLICY "Public can view available properties" ON properties 
        FOR SELECT USING (status IN ('available', 'sold', 'rented'));
      `
    });

    if (dropError) {
      console.error('Error fixing policies:', dropError);
      return NextResponse.json({ 
        success: false, 
        error: dropError.message 
      }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      message: 'RLS policies fixed successfully'
    });

  } catch (err) {
    console.error('Unexpected error:', err);
    return NextResponse.json({ 
      success: false, 
      error: err instanceof Error ? err.message : 'Unknown error' 
    }, { status: 500 });
  }
}
