import { LoginForm } from '@/components/features/auth/login-form';
import Link from 'next/link';

export default function LoginPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-100 flex flex-col justify-center py-12 sm:px-6 lg:px-8 relative overflow-hidden">
      {/* Background decorations */}
      <div className="absolute top-0 left-0 w-full h-2 bg-gradient-to-r from-italian-green via-white to-italian-red"></div>
      <div className="absolute top-10 right-10 w-32 h-32 bg-gradient-to-br from-italian-green/10 to-italian-red/10 rounded-full blur-xl"></div>
      <div className="absolute bottom-10 left-10 w-40 h-40 bg-gradient-to-br from-italian-red/10 to-italian-green/10 rounded-full blur-xl"></div>

      <div className="sm:mx-auto sm:w-full sm:max-w-md relative z-10">
        <Link href="/" className="flex items-center justify-center space-x-3 animate-fade-in">
          <div className="w-14 h-14 bg-gradient-to-br from-italian-green to-italian-red rounded-2xl flex items-center justify-center shadow-xl">
            <span className="text-white font-bold text-2xl">D</span>
          </div>
          <span className="font-bold text-3xl text-gray-900 bg-gradient-to-r from-italian-green to-italian-red bg-clip-text text-transparent">
            Darden Property & Management
          </span>
        </Link>
      </div>

      <div className="mt-12 sm:mx-auto sm:w-full sm:max-w-md relative z-10">
        <div className="bg-gradient-to-br from-white to-gray-50 py-12 px-8 shadow-2xl rounded-3xl border-0 animate-scale-in">
          <div className="space-y-8">
            <div className="text-center">
              <h2 className="text-4xl font-bold text-gray-900 mb-4 italian-flag-corner">
                Welcome Back
              </h2>
              <p className="text-lg text-gray-600">
                Sign in to your account
              </p>
              <p className="mt-4 text-gray-600">
                Or{' '}
                <Link
                  href="/auth/register"
                  className="font-bold text-italian-green hover:text-italian-red transition-colors duration-300"
                >
                  create a new account
                </Link>
              </p>
            </div>

            <LoginForm />

            <div className="text-center">
              <Link
                href="/auth/forgot-password"
                className="text-italian-green hover:text-italian-red font-medium transition-colors duration-300"
              >
                Forgot your password?
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
