import { supabase } from '@/lib/supabase/client';
import type { Database } from '@/types/database';

type Property = Database['public']['Tables']['properties']['Row'];
type PropertyInsert = Database['public']['Tables']['properties']['Insert'];
type PropertyUpdate = Database['public']['Tables']['properties']['Update'];

export interface PropertySearchFilters {
  searchQuery?: string;
  propertyType?: string;
  status?: string;
  city?: string;
  region?: string;
  minPrice?: number;
  maxPrice?: number;
  minBedrooms?: number;
  maxBedrooms?: number;
  minBathrooms?: number;
  maxBathrooms?: number;
  minArea?: number;
  maxArea?: number;
  features?: string[];
}

export interface PropertySearchOptions {
  limit?: number;
  offset?: number;
  sortBy?: 'created_at' | 'price' | 'area' | 'title';
  sortOrder?: 'asc' | 'desc';
}

export class PropertyService {
  static async searchProperties(
    filters: PropertySearchFilters = {},
    options: PropertySearchOptions = {}
  ) {
    const {
      searchQuery,
      propertyType,
      status,
      city,
      region,
      minPrice,
      maxPrice,
      minBedrooms,
      maxBedrooms,
      minBathrooms,
      maxBathrooms,
      minArea,
      maxArea,
      features,
    } = filters;

    const {
      limit = 20,
      offset = 0,
      sortBy = 'created_at',
      sortOrder = 'desc',
    } = options;

    let query = supabase
      .from('properties')
      .select('*', { count: 'exact' })
      .range(offset, offset + limit - 1)
      .order(sortBy, { ascending: sortOrder === 'asc' });

    // Apply filters
    if (searchQuery) {
      query = query.or(`title.ilike.%${searchQuery}%,description.ilike.%${searchQuery}%,address.ilike.%${searchQuery}%`);
    }

    if (propertyType && propertyType !== 'all') {
      query = query.eq('property_type', propertyType as 'apartment' | 'house' | 'villa' | 'commercial' | 'land');
    }

    if (status && status !== 'all') {
      query = query.eq('status', status as 'available' | 'sold' | 'rented' | 'pending');
    }

    if (city && city !== 'all') {
      query = query.eq('city', city);
    }

    if (region && region !== 'all') {
      query = query.eq('region', region);
    }

    if (minPrice !== undefined) {
      query = query.gte('price', minPrice);
    }

    if (maxPrice !== undefined) {
      query = query.lte('price', maxPrice);
    }

    if (minBedrooms !== undefined) {
      query = query.gte('bedrooms', minBedrooms);
    }

    if (maxBedrooms !== undefined) {
      query = query.lte('bedrooms', maxBedrooms);
    }

    if (minBathrooms !== undefined) {
      query = query.gte('bathrooms', minBathrooms);
    }

    if (maxBathrooms !== undefined) {
      query = query.lte('bathrooms', maxBathrooms);
    }

    if (minArea !== undefined) {
      query = query.gte('area', minArea);
    }

    if (maxArea !== undefined) {
      query = query.lte('area', maxArea);
    }

    if (features && features.length > 0) {
      query = query.overlaps('features', features);
    }

    const { data, error, count } = await query;

    if (error) {
      throw error;
    }

    return {
      properties: data || [],
      totalCount: count || 0,
    };
  }

  static async getProperty(id: string): Promise<Property | null> {
    const { data, error } = await supabase
      .from('properties')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      throw error;
    }

    return data;
  }

  static async createProperty(property: PropertyInsert): Promise<Property> {
    const { data, error } = await supabase
      .from('properties')
      .insert(property)
      .select()
      .single();

    if (error) {
      throw error;
    }

    return data;
  }

  static async updateProperty(id: string, updates: PropertyUpdate): Promise<Property> {
    const { data, error } = await supabase
      .from('properties')
      .update({ ...updates, updated_at: new Date().toISOString() })
      .eq('id', id)
      .select()
      .single();

    if (error) {
      throw error;
    }

    return data;
  }

  static async deleteProperty(id: string): Promise<void> {
    const { error } = await supabase
      .from('properties')
      .delete()
      .eq('id', id);

    if (error) {
      throw error;
    }
  }

  static async getFeaturedProperties(limit: number = 6): Promise<Property[]> {
    const { data, error } = await supabase
      .from('properties')
      .select('*')
      .eq('status', 'available')
      .order('created_at', { ascending: false })
      .limit(limit);

    if (error) {
      throw error;
    }

    return data || [];
  }

  static async getSimilarProperties(
    propertyId: string,
    propertyType: string,
    city: string,
    limit: number = 4
  ): Promise<Property[]> {
    const { data, error } = await supabase
      .from('properties')
      .select('*')
      .eq('property_type', propertyType as 'apartment' | 'house' | 'villa' | 'commercial' | 'land')
      .eq('city', city)
      .eq('status', 'available')
      .neq('id', propertyId)
      .order('created_at', { ascending: false })
      .limit(limit);

    if (error) {
      throw error;
    }

    return data || [];
  }

  static async getPropertyStats() {
    const { data, error } = await supabase
      .from('properties')
      .select('status, price, created_at');

    if (error) {
      throw error;
    }

    const properties = data || [];
    const totalProperties = properties.length;
    const availableProperties = properties.filter(p => p.status === 'available').length;
    const soldProperties = properties.filter(p => p.status === 'sold').length;
    const rentedProperties = properties.filter(p => p.status === 'rented').length;
    const pendingProperties = properties.filter(p => p.status === 'pending').length;
    
    const totalValue = properties.reduce((sum, p) => sum + (p.price || 0), 0);
    const averagePrice = totalProperties > 0 ? totalValue / totalProperties : 0;
    
    // Recent listings (last 30 days)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    const recentListings = properties.filter(p => 
      new Date(p.created_at) >= thirtyDaysAgo
    ).length;

    return {
      totalProperties,
      availableProperties,
      soldProperties,
      rentedProperties,
      pendingProperties,
      totalValue,
      averagePrice,
      recentListings,
    };
  }

  static async getCities(): Promise<string[]> {
    const { data, error } = await supabase
      .from('properties')
      .select('city')
      .not('city', 'is', null);

    if (error) {
      throw error;
    }

    const cities = [...new Set(data?.map(p => p.city).filter(Boolean) || [])] as string[];
    return cities.sort();
  }

  static async getRegions(): Promise<string[]> {
    const { data, error } = await supabase
      .from('properties')
      .select('region')
      .not('region', 'is', null);

    if (error) {
      throw error;
    }

    const regions = [...new Set(data?.map(p => p.region).filter(Boolean) || [])] as string[];
    return regions.sort();
  }
}
