'use client';

import { useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  MapPin, 
  Navigation, 
  Layers, 
  Maximize2,
  Home,
  Building,
  Car,
  ShoppingBag,
  GraduationCap,
  Hospital
} from 'lucide-react';

interface PropertyMapProps {
  properties?: Array<{
    id: string;
    title: string;
    price: number;
    currency: string;
    location: {
      lat: number;
      lng: number;
    };
    type: string;
    status: string;
  }>;
  center?: {
    lat: number;
    lng: number;
  };
  zoom?: number;
}

const mockProperties = [
  {
    id: '1',
    title: 'Luxury Villa in Casablanca',
    price: 2500000,
    currency: 'MAD',
    location: { lat: 33.5731, lng: -7.5898 },
    type: 'villa',
    status: 'available',
  },
  {
    id: '2',
    title: 'Modern Apartment in Rabat',
    price: 850000,
    currency: 'MAD',
    location: { lat: 34.0209, lng: -6.8416 },
    type: 'apartment',
    status: 'available',
  },
  {
    id: '3',
    title: 'Traditional Riad in Marrakech',
    price: 1200000,
    currency: 'MAD',
    location: { lat: 31.6295, lng: -7.9811 },
    type: 'house',
    status: 'sold',
  },
];

const nearbyAmenities = [
  { type: 'school', icon: GraduationCap, label: 'Schools', count: 5 },
  { type: 'hospital', icon: Hospital, label: 'Healthcare', count: 3 },
  { type: 'shopping', icon: ShoppingBag, label: 'Shopping', count: 8 },
  { type: 'transport', icon: Car, label: 'Transport', count: 12 },
];

export function PropertyMap({
  properties = mockProperties
}: PropertyMapProps) {
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const center = { lat: 33.5731, lng: -7.5898 };
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const zoom = 10;
  const [selectedProperty, setSelectedProperty] = useState<string | null>(null);
  const [showAmenities, setShowAmenities] = useState(false);
  const [mapType, setMapType] = useState<'roadmap' | 'satellite'>('roadmap');

  const getPropertyIcon = (type: string) => {
    switch (type) {
      case 'villa':
      case 'house':
        return Home;
      case 'apartment':
        return Building;
      default:
        return MapPin;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'available': return 'bg-green-500';
      case 'sold': return 'bg-red-500';
      case 'rented': return 'bg-blue-500';
      case 'pending': return 'bg-yellow-500';
      default: return 'bg-gray-500';
    }
  };

  return (
    <div className="space-y-4">
      {/* Map Controls */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Button
            variant={mapType === 'roadmap' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setMapType('roadmap')}
          >
            <MapPin className="mr-2 h-4 w-4" />
            Map
          </Button>
          <Button
            variant={mapType === 'satellite' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setMapType('satellite')}
          >
            <Layers className="mr-2 h-4 w-4" />
            Satellite
          </Button>
          <Button
            variant={showAmenities ? 'default' : 'outline'}
            size="sm"
            onClick={() => setShowAmenities(!showAmenities)}
          >
            <Building className="mr-2 h-4 w-4" />
            Amenities
          </Button>
        </div>
        
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="sm">
            <Navigation className="mr-2 h-4 w-4" />
            Directions
          </Button>
          <Button variant="outline" size="sm">
            <Maximize2 className="mr-2 h-4 w-4" />
            Fullscreen
          </Button>
        </div>
      </div>

      {/* Map Container */}
      <Card>
        <CardContent className="p-0">
          <div className="relative aspect-[16/10] bg-gray-200 rounded-lg overflow-hidden">
            {/* Mock Map Interface */}
            <div className="absolute inset-0 bg-gradient-to-br from-blue-100 to-green-100 flex items-center justify-center">
              <div className="text-center text-gray-600">
                <MapPin className="h-16 w-16 mx-auto mb-4" />
                <p className="text-lg font-medium">Interactive Map</p>
                <p className="text-sm">Google Maps / Mapbox Integration</p>
              </div>
            </div>

            {/* Property Markers */}
            {properties.map((property, index) => {
              const Icon = getPropertyIcon(property.type);
              return (
                <div
                  key={property.id}
                  className={`absolute cursor-pointer transform -translate-x-1/2 -translate-y-1/2 ${
                    selectedProperty === property.id ? 'z-20' : 'z-10'
                  }`}
                  style={{
                    left: `${20 + index * 25}%`,
                    top: `${30 + index * 15}%`,
                  }}
                  onClick={() => setSelectedProperty(
                    selectedProperty === property.id ? null : property.id
                  )}
                >
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center text-white shadow-lg ${
                    getStatusColor(property.status)
                  }`}>
                    <Icon className="h-4 w-4" />
                  </div>
                  
                  {selectedProperty === property.id && (
                    <div className="absolute top-10 left-1/2 transform -translate-x-1/2 w-64 bg-white rounded-lg shadow-xl p-4 border">
                      <h3 className="font-semibold text-gray-900 mb-2">
                        {property.title}
                      </h3>
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-lg font-bold text-primary">
                          {property.price.toLocaleString()} {property.currency}
                        </span>
                        <Badge variant="secondary">{property.type}</Badge>
                      </div>
                      <div className="flex space-x-2">
                        <Button size="sm" className="flex-1">View Details</Button>
                        <Button size="sm" variant="outline">Contact</Button>
                      </div>
                    </div>
                  )}
                </div>
              );
            })}

            {/* Amenity Markers */}
            {showAmenities && nearbyAmenities.map((amenity, index) => {
              const Icon = amenity.icon;
              return (
                <div
                  key={amenity.type}
                  className="absolute cursor-pointer transform -translate-x-1/2 -translate-y-1/2"
                  style={{
                    left: `${60 + index * 10}%`,
                    top: `${20 + index * 20}%`,
                  }}
                >
                  <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center text-white shadow-md">
                    <Icon className="h-3 w-3" />
                  </div>
                </div>
              );
            })}

            {/* Map Legend */}
            <div className="absolute bottom-4 left-4 bg-white rounded-lg shadow-lg p-3">
              <h4 className="text-sm font-medium text-gray-900 mb-2">Legend</h4>
              <div className="space-y-1 text-xs">
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                  <span>Available</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                  <span>Sold</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                  <span>Rented</span>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Nearby Amenities */}
      {showAmenities && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Nearby Amenities</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {nearbyAmenities.map((amenity) => {
                const Icon = amenity.icon;
                return (
                  <div key={amenity.type} className="text-center p-3 border rounded-lg">
                    <Icon className="h-8 w-8 text-blue-500 mx-auto mb-2" />
                    <div className="text-sm font-medium text-gray-900">
                      {amenity.label}
                    </div>
                    <div className="text-xs text-gray-500">
                      {amenity.count} nearby
                    </div>
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
