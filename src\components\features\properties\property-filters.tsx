'use client';

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { PROPERTY_TYPES, MOROCCAN_CITIES } from '@/constants/app';

export function PropertyFilters() {
  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Filters</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Price Range */}
          <div>
            <h3 className="font-medium mb-3">Price Range (MAD)</h3>
            <div className="grid grid-cols-2 gap-2">
              <Input placeholder="Min price" type="number" />
              <Input placeholder="Max price" type="number" />
            </div>
          </div>

          {/* Property Type */}
          <div>
            <h3 className="font-medium mb-3">Property Type</h3>
            <div className="space-y-2">
              {PROPERTY_TYPES.map((type) => (
                <label key={type.value} className="flex items-center space-x-2">
                  <input type="checkbox" className="rounded" />
                  <span className="text-sm">{type.label}</span>
                </label>
              ))}
            </div>
          </div>

          {/* Bedrooms */}
          <div>
            <h3 className="font-medium mb-3">Bedrooms</h3>
            <div className="grid grid-cols-4 gap-2">
              {[1, 2, 3, '4+'].map((num) => (
                <Button key={num} variant="outline" size="sm">
                  {num}
                </Button>
              ))}
            </div>
          </div>

          {/* Cities */}
          <div>
            <h3 className="font-medium mb-3">City</h3>
            <div className="space-y-2 max-h-40 overflow-y-auto">
              {MOROCCAN_CITIES.slice(0, 8).map((city) => (
                <label key={city} className="flex items-center space-x-2">
                  <input type="checkbox" className="rounded" />
                  <span className="text-sm">{city}</span>
                </label>
              ))}
            </div>
          </div>

          <Button className="w-full">Apply Filters</Button>
        </CardContent>
      </Card>
    </div>
  );
}
