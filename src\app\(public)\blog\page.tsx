import { BlogGrid } from '@/components/features/blog/blog-grid';
import { BlogFilters } from '@/components/features/blog/blog-filters';
import { Badge } from '@/components/ui/badge';

export default function BlogPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-100 py-16">
      <div className="container mx-auto px-4">
        {/* Hero Section */}
        <div className="text-center mb-20 relative">
          <div className="absolute inset-0 bg-gradient-to-r from-italian-green/5 via-white/50 to-italian-red/5 rounded-3xl -z-10"></div>
          <div className="relative py-16">
            <Badge variant="secondary" className="mb-6 bg-gradient-to-r from-italian-green to-italian-red text-white border-0 px-6 py-2 text-sm font-medium animate-fade-in">
              Blog & News
            </Badge>
            <h1 className="text-5xl lg:text-6xl font-bold text-gray-900 mb-8 italian-flag-corner animate-slide-up">
              Real Estate Insights & News
            </h1>
            <p className="text-xl lg:text-2xl text-gray-600 max-w-4xl mx-auto leading-relaxed animate-fade-in-delay">
              Stay informed with the latest trends, market analysis, and expert insights
              from Morocco&apos;s real estate industry.
            </p>
          </div>
        </div>

        {/* Blog Content */}
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-12">
          <div className="lg:col-span-1">
            <BlogFilters />
          </div>
          <div className="lg:col-span-3">
            <BlogGrid />
          </div>
        </div>
      </div>
    </div>
  );
}
